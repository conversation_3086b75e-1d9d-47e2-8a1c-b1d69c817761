'use client';

import { memo } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useLanguageStore } from '../../stores/languageStore';
import { StarIcon } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: { en: '<PERSON>', ar: 'سارة جونسون' },
    role: { en: 'Business Owner', ar: 'صاحبة أعمال' },
    content: {
      en: 'Amazing platform! The user experience is fantastic and the customer service is top-notch. Highly recommended!',
      ar: 'منصة رائعة! تجربة المستخدم ممتازة وخدمة العملاء من الدرجة الأولى. أنصح بها بشدة!'
    },
    avatar: '/images/avatars/sarah.jpg',
    rating: 5,
  },
  {
    id: 2,
    name: { en: '<PERSON> Al-Rashid', ar: 'أحمد الراشد' },
    role: { en: 'Tech Entrepreneur', ar: 'رائد أعمال تقني' },
    content: {
      en: 'The security features and payment integration are excellent. Perfect for our business needs.',
      ar: 'ميزات الأمان وتكامل الدفع ممتازة. مثالية لاحتياجات أعمالنا.'
    },
    avatar: '/images/avatars/ahmed.jpg',
    rating: 5,
  },
  {
    id: 3,
    name: { en: 'Maria Garcia', ar: 'ماريا غارسيا' },
    role: { en: 'Store Manager', ar: 'مديرة متجر' },
    content: {
      en: 'Easy to use, great features, and excellent support. Our sales have increased significantly!',
      ar: 'سهل الاستخدام، ميزات رائعة، ودعم ممتاز. زادت مبيعاتنا بشكل كبير!'
    },
    avatar: '/images/avatars/maria.jpg',
    rating: 5,
  },
];

const Testimonials = memo(function Testimonials() {
  const { language, t } = useLanguageStore();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('testimonials.title', 'What Our Customers Say')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('testimonials.description', 'Don\'t just take our word for it. Here\'s what our satisfied customers have to say about their experience.')}
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {testimonials.map((testimonial) => (
            <motion.div
              key={testimonial.id}
              variants={itemVariants}
              className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {Array.from({ length: testimonial.rating }).map((_, index) => (
                  <StarIcon key={index} className="w-5 h-5 text-yellow-400" />
                ))}
              </div>

              {/* Content */}
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.content[language]}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center">
                <div className="relative w-12 h-12 mr-4">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name[language]}
                    fill
                    className="rounded-full object-cover"
                    sizes="48px"
                  />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">
                    {testimonial.name[language]}
                  </div>
                  <div className="text-sm text-gray-600">
                    {testimonial.role[language]}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <div className="space-y-2">
            <div className="text-3xl font-bold text-blue-600">10K+</div>
            <div className="text-sm text-gray-600">{t('stats.customers', 'Happy Customers')}</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-green-600">98%</div>
            <div className="text-sm text-gray-600">{t('stats.satisfaction', 'Satisfaction Rate')}</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-purple-600">50K+</div>
            <div className="text-sm text-gray-600">{t('stats.orders', 'Orders Completed')}</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-orange-600">4.9</div>
            <div className="text-sm text-gray-600">{t('stats.rating', 'Average Rating')}</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
});

export default Testimonials;
