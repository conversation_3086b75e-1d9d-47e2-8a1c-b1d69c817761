"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_sections_Newsletter_tsx",{

/***/ "(app-pages-browser)/./src/components/sections/Newsletter.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Newsletter.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,MailIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,MailIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Newsletter = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function Newsletter() {\n    _s();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore)();\n    // Simple fallback translation function for now\n    const t = (key, fallback)=>fallback;\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubscribed, setIsSubscribed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(t('newsletter.errors.emailRequired', 'Email is required'));\n            return;\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(t('newsletter.errors.emailInvalid', 'Please enter a valid email address'));\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            setIsSubscribed(true);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(t('newsletter.success', 'Successfully subscribed to newsletter!'));\n            setEmail('');\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(t('newsletter.errors.general', 'Something went wrong. Please try again.'));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isSubscribed) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-gradient-to-r from-blue-600 to-purple-600\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"text-center text-white\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-16 h-16 mx-auto mb-4 text-green-300\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: t('newsletter.thankYou', 'Thank You!')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl opacity-90\",\n                            children: t('newsletter.confirmationMessage', 'You\\'ve been successfully subscribed to our newsletter.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gradient-to-r from-blue-600 to-purple-600\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"max-w-4xl mx-auto text-center text-white\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.6\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-8 h-8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                children: t('newsletter.title', 'Stay Updated')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl opacity-90 max-w-2xl mx-auto\",\n                                children: t('newsletter.description', 'Subscribe to our newsletter and be the first to know about new products, special offers, and exclusive deals.')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.form, {\n                        onSubmit: handleSubmit,\n                        className: \"max-w-md mx-auto\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"email\",\n                                        placeholder: t('newsletter.emailPlaceholder', 'Enter your email address'),\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 rounded-xl border-0 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50\",\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"px-8 py-3 bg-white text-blue-600 hover:bg-gray-100 rounded-xl font-semibold transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, this),\n                                            t('newsletter.subscribing', 'Subscribing...')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 19\n                                    }, this) : t('newsletter.subscribe', 'Subscribe')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                        className: \"text-sm opacity-75 mt-4\",\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 0.75\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        children: t('newsletter.privacy', 'We respect your privacy. Unsubscribe at any time.')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.7\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"\\uD83D\\uDCE7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: t('newsletter.benefits.updates', 'Latest Updates')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: t('newsletter.benefits.updatesDesc', 'Get notified about new features')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"\\uD83C\\uDF81\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: t('newsletter.benefits.offers', 'Exclusive Offers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: t('newsletter.benefits.offersDesc', 'Special discounts for subscribers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: t('newsletter.benefits.early', 'Early Access')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: t('newsletter.benefits.earlyDesc', 'Be first to try new products')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}, \"Y0/YKS4abwgFvElPiU2xlLUGZ6g=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore\n    ];\n})), \"Y0/YKS4abwgFvElPiU2xlLUGZ6g=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore\n    ];\n});\n_c1 = Newsletter;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Newsletter);\nvar _c, _c1;\n$RefreshReg$(_c, \"Newsletter$memo\");\n$RefreshReg$(_c1, \"Newsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Newsletter.tsx\n"));

/***/ })

});