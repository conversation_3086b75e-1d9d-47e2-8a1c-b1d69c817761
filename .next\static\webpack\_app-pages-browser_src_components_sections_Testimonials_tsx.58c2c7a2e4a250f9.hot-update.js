"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_sections_Testimonials_tsx",{

/***/ "(app-pages-browser)/./src/components/sections/Testimonials.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/Testimonials.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst testimonials = [\n    {\n        id: 1,\n        name: {\n            en: 'Sarah Johnson',\n            ar: 'سارة جونسون'\n        },\n        role: {\n            en: 'Business Owner',\n            ar: 'صاحبة أعمال'\n        },\n        content: {\n            en: 'Amazing platform! The user experience is fantastic and the customer service is top-notch. Highly recommended!',\n            ar: 'منصة رائعة! تجربة المستخدم ممتازة وخدمة العملاء من الدرجة الأولى. أنصح بها بشدة!'\n        },\n        avatar: '/images/avatars/sarah.jpg',\n        rating: 5\n    },\n    {\n        id: 2,\n        name: {\n            en: 'Ahmed Al-Rashid',\n            ar: 'أحمد الراشد'\n        },\n        role: {\n            en: 'Tech Entrepreneur',\n            ar: 'رائد أعمال تقني'\n        },\n        content: {\n            en: 'The security features and payment integration are excellent. Perfect for our business needs.',\n            ar: 'ميزات الأمان وتكامل الدفع ممتازة. مثالية لاحتياجات أعمالنا.'\n        },\n        avatar: '/images/avatars/ahmed.jpg',\n        rating: 5\n    },\n    {\n        id: 3,\n        name: {\n            en: 'Maria Garcia',\n            ar: 'ماريا غارسيا'\n        },\n        role: {\n            en: 'Store Manager',\n            ar: 'مديرة متجر'\n        },\n        content: {\n            en: 'Easy to use, great features, and excellent support. Our sales have increased significantly!',\n            ar: 'سهل الاستخدام، ميزات رائعة، ودعم ممتاز. زادت مبيعاتنا بشكل كبير!'\n        },\n        avatar: '/images/avatars/maria.jpg',\n        rating: 5\n    }\n];\nconst Testimonials = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function Testimonials() {\n    _s();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore)();\n    // Simple fallback translation function for now\n    const t = (key, fallback)=>fallback;\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: 'easeOut'\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('testimonials.title', 'What Our Customers Say')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t('testimonials.description', 'Don\\'t just take our word for it. Here\\'s what our satisfied customers have to say about their experience.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            className: \"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: Array.from({\n                                        length: testimonial.rating\n                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-400\"\n                                        }, index, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content[language],\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-12 h-12 mr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: testimonial.avatar,\n                                                alt: testimonial.name[language],\n                                                fill: true,\n                                                className: \"rounded-full object-cover\",\n                                                sizes: \"48px\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name[language]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: testimonial.role[language]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, testimonial.id, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: \"10K+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.customers', 'Happy Customers')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-green-600\",\n                                    children: \"98%\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.satisfaction', 'Satisfaction Rate')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-purple-600\",\n                                    children: \"50K+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.orders', 'Orders Completed')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-orange-600\",\n                                    children: \"4.9\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.rating', 'Average Rating')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}, \"U9lZRyUdG8HIwpHFfafb+brsRy0=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n})), \"U9lZRyUdG8HIwpHFfafb+brsRy0=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n});\n_c1 = Testimonials;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);\nvar _c, _c1;\n$RefreshReg$(_c, \"Testimonials$memo\");\n$RefreshReg$(_c1, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Testimonials.tsx\n"));

/***/ })

});