"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_sections_Testimonials_tsx",{

/***/ "(app-pages-browser)/./src/components/sections/Testimonials.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/Testimonials.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst testimonials = [\n    {\n        id: 1,\n        name: {\n            en: 'Sarah Johnson',\n            ar: 'سارة جونسون'\n        },\n        role: {\n            en: 'Business Owner',\n            ar: 'صاحبة أعمال'\n        },\n        content: {\n            en: 'Amazing platform! The user experience is fantastic and the customer service is top-notch. Highly recommended!',\n            ar: 'منصة رائعة! تجربة المستخدم ممتازة وخدمة العملاء من الدرجة الأولى. أنصح بها بشدة!'\n        },\n        avatar: '/images/avatars/sarah.jpg',\n        rating: 5\n    },\n    {\n        id: 2,\n        name: {\n            en: 'Ahmed Al-Rashid',\n            ar: 'أحمد الراشد'\n        },\n        role: {\n            en: 'Tech Entrepreneur',\n            ar: 'رائد أعمال تقني'\n        },\n        content: {\n            en: 'The security features and payment integration are excellent. Perfect for our business needs.',\n            ar: 'ميزات الأمان وتكامل الدفع ممتازة. مثالية لاحتياجات أعمالنا.'\n        },\n        avatar: '/images/avatars/ahmed.jpg',\n        rating: 5\n    },\n    {\n        id: 3,\n        name: {\n            en: 'Maria Garcia',\n            ar: 'ماريا غارسيا'\n        },\n        role: {\n            en: 'Store Manager',\n            ar: 'مديرة متجر'\n        },\n        content: {\n            en: 'Easy to use, great features, and excellent support. Our sales have increased significantly!',\n            ar: 'سهل الاستخدام، ميزات رائعة، ودعم ممتاز. زادت مبيعاتنا بشكل كبير!'\n        },\n        avatar: '/images/avatars/maria.jpg',\n        rating: 5\n    }\n];\nconst Testimonials = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function Testimonials() {\n    _s();\n    const { language, t } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore)();\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: 'easeOut'\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('testimonials.title', 'What Our Customers Say')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t('testimonials.description', 'Don\\'t just take our word for it. Here\\'s what our satisfied customers have to say about their experience.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            className: \"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: Array.from({\n                                        length: testimonial.rating\n                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-400\"\n                                        }, index, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content[language],\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-12 h-12 mr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: testimonial.avatar,\n                                                alt: testimonial.name[language],\n                                                fill: true,\n                                                className: \"rounded-full object-cover\",\n                                                sizes: \"48px\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name[language]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: testimonial.role[language]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, testimonial.id, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-blue-600\",\n                                    children: \"10K+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.customers', 'Happy Customers')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-green-600\",\n                                    children: \"98%\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.satisfaction', 'Satisfaction Rate')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-purple-600\",\n                                    children: \"50K+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.orders', 'Orders Completed')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-orange-600\",\n                                    children: \"4.9\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('stats.rating', 'Average Rating')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Testimonials.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}, \"XNQ4WLAwAE3VVi3akpcXCBfgzsw=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n})), \"XNQ4WLAwAE3VVi3akpcXCBfgzsw=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n});\n_c1 = Testimonials;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);\nvar _c, _c1;\n$RefreshReg$(_c, \"Testimonials$memo\");\n$RefreshReg$(_c1, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Testimonials.tsx\n"));

/***/ })

});