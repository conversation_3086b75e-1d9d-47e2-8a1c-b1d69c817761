'use client';

import { memo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useLanguageStore } from '../../stores/languageStore';
import { ArrowRightIcon } from 'lucide-react';

const categories = [
  {
    id: 'electronics',
    name: { en: 'Electronics', ar: 'إلكترونيات' },
    description: { en: 'Latest gadgets and devices', ar: 'أحدث الأجهزة والتقنيات' },
    image: '/images/categories/electronics.jpg',
    href: '/shop?category=electronics',
    color: 'from-blue-500 to-cyan-500',
  },
  {
    id: 'fashion',
    name: { en: 'Fashion', ar: 'أزياء' },
    description: { en: 'Trendy clothing and accessories', ar: 'ملابس وإكسسوارات عصرية' },
    image: '/images/categories/fashion.jpg',
    href: '/shop?category=fashion',
    color: 'from-pink-500 to-rose-500',
  },
  {
    id: 'home',
    name: { en: 'Home & Garden', ar: 'منزل وحديقة' },
    description: { en: 'Everything for your home', ar: 'كل ما تحتاجه لمنزلك' },
    image: '/images/categories/home.jpg',
    href: '/shop?category=home',
    color: 'from-green-500 to-emerald-500',
  },
  {
    id: 'sports',
    name: { en: 'Sports & Fitness', ar: 'رياضة ولياقة' },
    description: { en: 'Gear for active lifestyle', ar: 'معدات للحياة النشطة' },
    image: '/images/categories/sports.jpg',
    href: '/shop?category=sports',
    color: 'from-orange-500 to-amber-500',
  },
  {
    id: 'beauty',
    name: { en: 'Beauty & Health', ar: 'جمال وصحة' },
    description: { en: 'Care for your wellbeing', ar: 'اعتني بصحتك وجمالك' },
    image: '/images/categories/beauty.jpg',
    href: '/shop?category=beauty',
    color: 'from-purple-500 to-violet-500',
  },
  {
    id: 'automotive',
    name: { en: 'Automotive', ar: 'سيارات' },
    description: { en: 'Car parts and accessories', ar: 'قطع غيار وإكسسوارات السيارات' },
    image: '/images/categories/automotive.jpg',
    href: '/shop?category=automotive',
    color: 'from-gray-600 to-slate-600',
  },
];

const Categories = memo(function Categories() {
  const { language } = useLanguageStore();
  const isRTL = language === 'ar';

  // Simple translation function
  const t = (key: string, fallback: string) => fallback;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('categories.title', 'Shop by Category')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('categories.description', 'Explore our wide range of product categories and find exactly what you\'re looking for.')}
          </p>
        </motion.div>

        {/* Categories Grid */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {categories.map((category) => (
            <motion.div key={category.id} variants={itemVariants}>
              <Link href={category.href} className="group block">
                <div className="relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  {/* Image Container */}
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={category.image}
                      alt={category.name[language]}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-110"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />

                    {/* Gradient Overlay */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>

                    {/* Category Badge */}
                    <div className="absolute top-4 left-4">
                      <span className="inline-block px-3 py-1 bg-white/90 backdrop-blur-sm text-gray-800 text-sm font-medium rounded-full">
                        {category.name[language]}
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                      {category.name[language]}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {category.description[language]}
                    </p>

                    {/* CTA */}
                    <div className="flex items-center text-blue-600 font-medium group-hover:text-blue-700 transition-colors duration-300">
                      <span className="mr-2">{t('categories.shopNow', 'Shop Now')}</span>
                      <ArrowRightIcon className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${isRTL ? 'rotate-180 group-hover:-translate-x-1' : ''}`} />
                    </div>
                  </div>

                  {/* Hover Effect Border */}
                  <div className="absolute inset-0 border-2 border-transparent group-hover:border-blue-200 rounded-2xl transition-colors duration-300"></div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <p className="text-gray-600 mb-4">
            {t('categories.bottomText', 'Can\'t find what you\'re looking for?')}
          </p>
          <Link
            href="/shop"
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300"
          >
            {t('categories.browseAll', 'Browse All Products')}
            <ArrowRightIcon className={`w-4 h-4 ${isRTL ? 'rotate-180' : ''}`} />
          </Link>
        </motion.div>
      </div>
    </section>
  );
});

export default Categories;
