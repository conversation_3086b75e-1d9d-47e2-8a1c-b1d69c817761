'use client';

import { memo } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useLanguageStore } from '../../stores/languageStore';
import { Button } from '../ui/Button';
import {
  TruckIcon,
  ShieldCheckIcon,
  MessageCircleIcon,
  CreditCardIcon,
  ArrowRightIcon
} from 'lucide-react';

const services = [
  {
    id: 'shipping',
    icon: TruckIcon,
    title: { en: 'Free Shipping', ar: 'شحن مجاني' },
    description: { en: 'Free shipping on orders over $200', ar: 'شحن مجاني للطلبات أكثر من 200 ريال' },
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    id: 'security',
    icon: ShieldCheckIcon,
    title: { en: 'Secure Payment', ar: 'دفع آمن' },
    description: { en: '100% secure payment processing', ar: 'معالجة دفع آمنة 100%' },
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    id: 'support',
    icon: MessageCircleIcon,
    title: { en: '24/7 Support', ar: 'دعم 24/7' },
    description: { en: 'Round-the-clock customer support', ar: 'دعم العملاء على مدار الساعة' },
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
  },
  {
    id: 'returns',
    icon: CreditCardIcon,
    title: { en: 'Easy Returns', ar: 'إرجاع سهل' },
    description: { en: '30-day hassle-free returns', ar: 'إرجاع بدون متاعب لمدة 30 يوم' },
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
  },
];

const FeaturedServices = memo(function FeaturedServices() {
  const { language } = useLanguageStore();
  const isRTL = language === 'ar';

  // Simple translation function
  const t = (key: string, fallback: string) => fallback;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('services.title', 'Why Choose Us')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('services.description', 'We provide exceptional service and support to ensure your shopping experience is seamless and enjoyable.')}
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {services.map((service) => {
            const IconComponent = service.icon;
            return (
              <motion.div
                key={service.id}
                variants={itemVariants}
                className="text-center group"
              >
                <div className={`inline-flex items-center justify-center w-16 h-16 ${service.bgColor} rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className={`w-8 h-8 ${service.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {service.title[language]}
                </h3>
                <p className="text-gray-600">
                  {service.description[language]}
                </p>
              </motion.div>
            );
          })}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-16 p-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('services.cta.title', 'Need Help?')}
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            {t('services.cta.description', 'Our customer service team is here to help you with any questions or concerns you may have.')}
          </p>
          <Button
            asChild
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-xl"
          >
            <Link href="/contact" className="flex items-center gap-2">
              {t('services.cta.button', 'Contact Support')}
              <ArrowRightIcon className={`w-5 h-5 ${isRTL ? 'rotate-180' : ''}`} />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
});

export default FeaturedServices;
