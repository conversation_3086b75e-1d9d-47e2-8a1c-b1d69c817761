/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/[locale]/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CRootLayout.tsx%22%2C%22ids%22%3A%5B%22RootLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Cindex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CRootLayout.tsx%22%2C%22ids%22%3A%5B%22RootLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Cindex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\"],\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(app-pages-browser)/./src/app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/RootLayout.tsx */ \"(app-pages-browser)/./src/components/layout/RootLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/index.css */ \"(app-pages-browser)/./src/index.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CRootLayout.tsx%22%2C%22ids%22%3A%5B%22RootLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Cindex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/RootLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/RootLayout.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RootLayout: () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _security_CsrfToken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../security/CsrfToken */ \"(app-pages-browser)/./src/components/security/CsrfToken.tsx\");\n/* harmony import */ var _performance_Prefetcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../performance/Prefetcher */ \"(app-pages-browser)/./src/components/performance/Prefetcher.tsx\");\n/* harmony import */ var _ui_MobileOptimizer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/MobileOptimizer */ \"(app-pages-browser)/./src/components/ui/MobileOptimizer.tsx\");\n/* __next_internal_client_entry_do_not_use__ RootLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RootLayout(param) {\n    let { children } = param;\n    _s();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_1__.useLanguageStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_security_CsrfToken__WEBPACK_IMPORTED_MODULE_2__.CsrfToken, {}, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\layout\\\\RootLayout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_performance_Prefetcher__WEBPACK_IMPORTED_MODULE_3__.Prefetcher, {}, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\layout\\\\RootLayout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_MobileOptimizer__WEBPACK_IMPORTED_MODULE_4__.MobileOptimizer, {}, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\layout\\\\RootLayout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true);\n}\n_s(RootLayout, \"U9lZRyUdG8HIwpHFfafb+brsRy0=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_1__.useLanguageStore\n    ];\n});\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9Sb290TGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUc4RDtBQUNaO0FBQ0s7QUFDQztBQU1qRCxTQUFTSSxXQUFXLEtBQTZCO1FBQTdCLEVBQUVDLFFBQVEsRUFBbUIsR0FBN0I7O0lBQ3pCLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUdOLHVFQUFnQkE7SUFFckMscUJBQ0U7OzBCQUNFLDhEQUFDQywwREFBU0E7Ozs7OzBCQUNWLDhEQUFDQywrREFBVUE7Ozs7OzBCQUNYLDhEQUFDQyxnRUFBZUE7Ozs7O1lBQ2ZFOzs7QUFHUDtHQVhnQkQ7O1FBQ09KLG1FQUFnQkE7OztLQUR2QkkiLCJzb3VyY2VzIjpbIkQ6XFxlY29tbWVyY2Vwcm9cXHNyY1xcY29tcG9uZW50c1xcbGF5b3V0XFxSb290TGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUxhbmd1YWdlU3RvcmUgfSBmcm9tICcuLi8uLi9zdG9yZXMvbGFuZ3VhZ2VTdG9yZSc7XG5pbXBvcnQgeyBDc3JmVG9rZW4gfSBmcm9tICcuLi9zZWN1cml0eS9Dc3JmVG9rZW4nO1xuaW1wb3J0IHsgUHJlZmV0Y2hlciB9IGZyb20gJy4uL3BlcmZvcm1hbmNlL1ByZWZldGNoZXInO1xuaW1wb3J0IHsgTW9iaWxlT3B0aW1pemVyIH0gZnJvbSAnLi4vdWkvTW9iaWxlT3B0aW1pemVyJztcblxuaW50ZXJmYWNlIFJvb3RMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfTogUm9vdExheW91dFByb3BzKSB7XG4gIGNvbnN0IHsgbGFuZ3VhZ2UgfSA9IHVzZUxhbmd1YWdlU3RvcmUoKTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8Q3NyZlRva2VuIC8+XG4gICAgICA8UHJlZmV0Y2hlciAvPlxuICAgICAgPE1vYmlsZU9wdGltaXplciAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvPlxuICApO1xufSJdLCJuYW1lcyI6WyJ1c2VMYW5ndWFnZVN0b3JlIiwiQ3NyZlRva2VuIiwiUHJlZmV0Y2hlciIsIk1vYmlsZU9wdGltaXplciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImxhbmd1YWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/RootLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/performance/Prefetcher.tsx":
/*!***************************************************!*\
  !*** ./src/components/performance/Prefetcher.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Prefetcher: () => (/* binding */ Prefetcher)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _data_products__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/products */ \"(app-pages-browser)/./src/data/products.ts\");\n/* harmony import */ var _data_services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../data/services */ \"(app-pages-browser)/./src/data/services.ts\");\n/* harmony import */ var _data_clearanceItems__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../data/clearanceItems */ \"(app-pages-browser)/./src/data/clearanceItems.ts\");\n/* harmony import */ var _data_blogPosts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../data/blogPosts */ \"(app-pages-browser)/./src/data/blogPosts.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_localCache__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/localCache */ \"(app-pages-browser)/./src/lib/localCache.ts\");\n/* __next_internal_client_entry_do_not_use__ Prefetcher auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\n * مكون لتحميل البيانات المسبق لتحسين الأداء\n * يقوم بتحميل البيانات المتوقع استخدامها في الصفحات التالية\n */ function Prefetcher() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient)();\n    // تحميل البيانات المسبق بناءً على المسار الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Prefetcher.useEffect\": ()=>{\n            const prefetchData = {\n                \"Prefetcher.useEffect.prefetchData\": async ()=>{\n                    const path = pathname;\n                    // الصفحة الرئيسية\n                    if (path === '/') {\n                        // تحميل المنتجات المميزة مسبقًا\n                        const featuredProducts = _data_products__WEBPACK_IMPORTED_MODULE_2__.products.filter({\n                            \"Prefetcher.useEffect.prefetchData.featuredProducts\": (product)=>product.featured\n                        }[\"Prefetcher.useEffect.prefetchData.featuredProducts\"]).slice(0, 4);\n                        queryClient.setQueryData([\n                            'featured-products'\n                        ], featuredProducts);\n                        // تحميل عناصر التصفية مسبقًا\n                        queryClient.setQueryData([\n                            'clearance-items'\n                        ], _data_clearanceItems__WEBPACK_IMPORTED_MODULE_4__.clearanceItems.slice(0, 3));\n                        // تخزين البيانات في التخزين المؤقت المحلي\n                        _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set('featured-products', featuredProducts);\n                        _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set('clearance-items', _data_clearanceItems__WEBPACK_IMPORTED_MODULE_4__.clearanceItems.slice(0, 3));\n                    } else if (path === '/shop') {\n                        // تحميل جميع المنتجات مسبقًا\n                        queryClient.setQueryData([\n                            'products'\n                        ], _data_products__WEBPACK_IMPORTED_MODULE_2__.products);\n                        _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set('products', _data_products__WEBPACK_IMPORTED_MODULE_2__.products);\n                    } else if (path.startsWith('/shop/product/')) {\n                        const slug = path.split('/').pop();\n                        const product = _data_products__WEBPACK_IMPORTED_MODULE_2__.products.find({\n                            \"Prefetcher.useEffect.prefetchData.product\": (p)=>p.slug === slug\n                        }[\"Prefetcher.useEffect.prefetchData.product\"]);\n                        if (product) {\n                            // تحميل المنتج الحالي مسبقًا\n                            queryClient.setQueryData([\n                                'product',\n                                slug\n                            ], product);\n                            // تحميل المنتجات ذات الصلة مسبقًا\n                            const relatedProducts = _data_products__WEBPACK_IMPORTED_MODULE_2__.products.filter({\n                                \"Prefetcher.useEffect.prefetchData.relatedProducts\": (p)=>p.category === product.category && p.id !== product.id\n                            }[\"Prefetcher.useEffect.prefetchData.relatedProducts\"]).slice(0, 4);\n                            queryClient.setQueryData([\n                                'related-products',\n                                slug\n                            ], relatedProducts);\n                            // تخزين البيانات في التخزين المؤقت المحلي\n                            _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set(\"product-\".concat(slug), product);\n                            _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set(\"related-products-\".concat(slug), relatedProducts);\n                        }\n                    } else if (path === '/services') {\n                        // تحميل جميع الخدمات مسبقًا\n                        queryClient.setQueryData([\n                            'services'\n                        ], _data_services__WEBPACK_IMPORTED_MODULE_3__.services);\n                        _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set('services', _data_services__WEBPACK_IMPORTED_MODULE_3__.services);\n                    } else if (path === '/blog') {\n                        // تحميل منشورات المدونة مسبقًا\n                        queryClient.setQueryData([\n                            'blog-posts'\n                        ], _data_blogPosts__WEBPACK_IMPORTED_MODULE_5__.blogPosts);\n                        _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set('blog-posts', _data_blogPosts__WEBPACK_IMPORTED_MODULE_5__.blogPosts);\n                    } else if (path.startsWith('/blog/')) {\n                        const slug = path.split('/').pop();\n                        const post = _data_blogPosts__WEBPACK_IMPORTED_MODULE_5__.blogPosts.find({\n                            \"Prefetcher.useEffect.prefetchData.post\": (p)=>p.slug === slug\n                        }[\"Prefetcher.useEffect.prefetchData.post\"]);\n                        if (post) {\n                            // تحميل المنشور الحالي مسبقًا\n                            queryClient.setQueryData([\n                                'blog-post',\n                                slug\n                            ], post);\n                            // تحميل المنشورات ذات الصلة مسبقًا\n                            const relatedPosts = _data_blogPosts__WEBPACK_IMPORTED_MODULE_5__.blogPosts.filter({\n                                \"Prefetcher.useEffect.prefetchData.relatedPosts\": (p)=>p.categories.some({\n                                        \"Prefetcher.useEffect.prefetchData.relatedPosts\": (cat)=>post.categories.includes(cat)\n                                    }[\"Prefetcher.useEffect.prefetchData.relatedPosts\"]) && p.id !== post.id\n                            }[\"Prefetcher.useEffect.prefetchData.relatedPosts\"]).slice(0, 3);\n                            queryClient.setQueryData([\n                                'related-posts',\n                                slug\n                            ], relatedPosts);\n                            // تخزين البيانات في التخزين المؤقت المحلي\n                            _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set(\"blog-post-\".concat(slug), post);\n                            _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set(\"related-posts-\".concat(slug), relatedPosts);\n                        }\n                    }\n                    // تحميل البيانات من API خارجي للاختبار\n                    if (path === '/test-query') {\n                        try {\n                            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get('https://jsonplaceholder.typicode.com/todos/1');\n                            queryClient.setQueryData([\n                                'test',\n                                '1'\n                            ], response.data);\n                            _lib_localCache__WEBPACK_IMPORTED_MODULE_7__.defaultCache.set('test-1', response.data);\n                        } catch (error) {\n                            console.error('Error prefetching test data:', error);\n                        }\n                    }\n                }\n            }[\"Prefetcher.useEffect.prefetchData\"];\n            prefetchData();\n        }\n    }[\"Prefetcher.useEffect\"], [\n        pathname,\n        queryClient\n    ]);\n    // هذا المكون لا يعرض أي شيء في واجهة المستخدم\n    return null;\n}\n_s(Prefetcher, \"1XLUwpiqMO81AztJWjcUyIboVTs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient\n    ];\n});\n_c = Prefetcher;\nvar _c;\n$RefreshReg$(_c, \"Prefetcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/performance/Prefetcher.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/security/CsrfToken.tsx":
/*!***********************************************!*\
  !*** ./src/components/security/CsrfToken.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CsrfToken: () => (/* binding */ CsrfToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ CsrfToken auto */ var _s = $RefreshSig$();\n\n// مكون لإدارة CSRF Token\nfunction CsrfToken() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"CsrfToken.useEffect\": ()=>{\n            // وظيفة لجلب CSRF Token من الخادم\n            const fetchCsrfToken = {\n                \"CsrfToken.useEffect.fetchCsrfToken\": async ()=>{\n                    try {\n                        // في بيئة الإنتاج، سيتم استدعاء نقطة نهاية حقيقية\n                        // هنا نقوم بمحاكاة الاستجابة للتطوير المحلي\n                        const mockToken = \"csrf-\".concat(Math.random().toString(36).substring(2, 15));\n                        // إنشاء عنصر meta لتخزين CSRF Token\n                        let metaTag = document.querySelector('meta[name=\"csrf-token\"]');\n                        if (!metaTag) {\n                            metaTag = document.createElement('meta');\n                            metaTag.setAttribute('name', 'csrf-token');\n                            document.head.appendChild(metaTag);\n                        }\n                        metaTag.setAttribute('content', mockToken);\n                        setIsLoaded(true);\n                    } catch (error) {\n                        console.error('Failed to fetch CSRF token:', error);\n                    }\n                }\n            }[\"CsrfToken.useEffect.fetchCsrfToken\"];\n            fetchCsrfToken();\n        }\n    }[\"CsrfToken.useEffect\"], []);\n    // هذا المكون لا يعرض أي شيء في واجهة المستخدم\n    return null;\n}\n_s(CsrfToken, \"e/1lVN3R6kIvuSIAmUIHNmZXQsc=\");\n_c = CsrfToken;\nvar _c;\n$RefreshReg$(_c, \"CsrfToken\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/security/CsrfToken.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/MobileOptimizer.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/MobileOptimizer.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileOptimizer: () => (/* binding */ MobileOptimizer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ MobileOptimizer auto */ var _s = $RefreshSig$();\n\n\n/**\n * مكون لتحسين تجربة المستخدم على الأجهزة المحمولة\n * يقوم بتطبيق تحسينات مختلفة على الأجهزة المحمولة\n */ function MobileOptimizer() {\n    _s();\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    // التحقق مما إذا كان الجهاز محمولاً\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"MobileOptimizer.useEffect\": ()=>{\n            const checkMobile = {\n                \"MobileOptimizer.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"MobileOptimizer.useEffect.checkMobile\"];\n            // التحقق عند التحميل\n            checkMobile();\n            // التحقق عند تغيير حجم النافذة\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"MobileOptimizer.useEffect\": ()=>{\n                    window.removeEventListener('resize', checkMobile);\n                }\n            })[\"MobileOptimizer.useEffect\"];\n        }\n    }[\"MobileOptimizer.useEffect\"], []);\n    // تطبيق تحسينات الأجهزة المحمولة\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"MobileOptimizer.useEffect\": ()=>{\n            if (isMobile) {\n                // تحسين الأداء على الأجهزة المحمولة\n                // 1. تعطيل بعض الرسوم المتحركة الثقيلة\n                document.documentElement.classList.add('mobile-device');\n                // 2. تحسين حجم الخط للقراءة على الأجهزة المحمولة\n                document.documentElement.style.fontSize = resolvedTheme === 'dark' ? '15px' : '16px';\n                // 3. تحسين التمرير السلس\n                document.documentElement.style.scrollBehavior = 'smooth';\n                // 4. تحسين اللمس (زيادة مساحة النقر)\n                const style = document.createElement('style');\n                style.id = 'mobile-optimizations';\n                style.innerHTML = '\\n        .mobile-device button,\\n        .mobile-device a,\\n        .mobile-device input[type=\"button\"],\\n        .mobile-device input[type=\"submit\"] {\\n          min-height: 44px;\\n          min-width: 44px;\\n        }\\n\\n        .mobile-device .tap-target {\\n          padding: 0.5rem;\\n        }\\n\\n        /* تحسين أحجام الخطوط للأجهزة المحمولة */\\n        .mobile-device h1 {\\n          font-size: 1.75rem !important;\\n          line-height: 1.2 !important;\\n        }\\n\\n        .mobile-device h2 {\\n          font-size: 1.5rem !important;\\n          line-height: 1.25 !important;\\n        }\\n\\n        .mobile-device h3 {\\n          font-size: 1.25rem !important;\\n          line-height: 1.3 !important;\\n        }\\n\\n        /* تحسين المسافات بين العناصر */\\n        .mobile-device .container-custom {\\n          padding-left: 1rem !important;\\n          padding-right: 1rem !important;\\n        }\\n\\n        /* تحسين أزرار التفاعل */\\n        .mobile-device button,\\n        .mobile-device .button {\\n          padding: 0.625rem 1rem !important;\\n          border-radius: 0.5rem !important;\\n          font-weight: 500 !important;\\n        }\\n\\n        /* تحسين تجربة التمرير */\\n        .mobile-device {\\n          scroll-padding-top: 80px;\\n          -webkit-overflow-scrolling: touch;\\n        }\\n\\n        @media (max-width: 767px) {\\n          .container-custom {\\n            padding-left: 1rem;\\n            padding-right: 1rem;\\n          }\\n\\n          .section {\\n            padding-top: 2rem;\\n            padding-bottom: 2rem;\\n          }\\n\\n          h1 {\\n            font-size: 2rem;\\n            line-height: 1.2;\\n          }\\n\\n          h2 {\\n            font-size: 1.75rem;\\n            line-height: 1.2;\\n          }\\n\\n          .mobile-spacing {\\n            margin-bottom: 1.5rem;\\n          }\\n\\n          .mobile-stack {\\n            flex-direction: column;\\n          }\\n\\n          .mobile-full-width {\\n            width: 100%;\\n          }\\n\\n          .mobile-center {\\n            text-align: center;\\n          }\\n\\n          .mobile-hidden {\\n            display: none;\\n          }\\n\\n          .mobile-visible {\\n            display: block;\\n          }\\n\\n          .mobile-touch-scroll {\\n            -webkit-overflow-scrolling: touch;\\n          }\\n\\n          .mobile-no-scroll {\\n            overflow: hidden;\\n          }\\n        }\\n      ';\n                document.head.appendChild(style);\n                // 5. تحسين سرعة التحميل عن طريق تأخير تحميل الصور غير المرئية\n                const lazyImages = document.querySelectorAll('img[loading=\"lazy\"]');\n                if (lazyImages.length === 0) {\n                    const images = document.querySelectorAll('img:not([loading])');\n                    images.forEach({\n                        \"MobileOptimizer.useEffect\": (img)=>{\n                            if (!img.hasAttribute('loading')) {\n                                img.setAttribute('loading', 'lazy');\n                            }\n                        }\n                    }[\"MobileOptimizer.useEffect\"]);\n                }\n                return ({\n                    \"MobileOptimizer.useEffect\": ()=>{\n                        // إزالة التحسينات عند تغيير الجهاز\n                        document.documentElement.classList.remove('mobile-device');\n                        document.documentElement.style.fontSize = '';\n                        document.documentElement.style.scrollBehavior = '';\n                        const mobileStyle = document.getElementById('mobile-optimizations');\n                        if (mobileStyle) {\n                            mobileStyle.remove();\n                        }\n                    }\n                })[\"MobileOptimizer.useEffect\"];\n            }\n        }\n    }[\"MobileOptimizer.useEffect\"], [\n        isMobile,\n        resolvedTheme\n    ]);\n    // هذا المكون لا يعرض أي شيء في واجهة المستخدم\n    return null;\n}\n_s(MobileOptimizer, \"rvUmTholuJKVx/J1pkHc4XKeqEc=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme\n    ];\n});\n_c = MobileOptimizer;\nvar _c;\n$RefreshReg$(_c, \"MobileOptimizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/MobileOptimizer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/blogPosts.ts":
/*!*******************************!*\
  !*** ./src/data/blogPosts.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogCategories: () => (/* binding */ blogCategories),\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts)\n/* harmony export */ });\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'Global Supply Chain Trends 2025: Navigating the Future of Trade',\n        title_ar: undefined,\n        slug: 'global-supply-chain-trends-2025',\n        excerpt: 'Explore emerging trends in international trade and how businesses can adapt their supply chain strategies for success.',\n        excerpt_ar: undefined,\n        content: \"\\n      The landscape of global trade is rapidly evolving, driven by technological advancements, changing regulations, and shifting market dynamics. Understanding these trends is crucial for businesses looking to maintain competitive advantage in international markets.\\n\\n      ## Key Trends Shaping International Trade\\n\\n      ### 1. Digital Transformation\\n      The digitalization of trade processes is accelerating, with blockchain technology and AI playing pivotal roles in:\\n      - Supply chain transparency\\n      - Documentation automation\\n      - Risk management\\n      - Trade finance\\n\\n      ### 2. Sustainable Logistics\\n      Environmental consciousness is becoming a core consideration in trade decisions:\\n      - Green shipping alternatives\\n      - Carbon footprint tracking\\n      - Sustainable packaging solutions\\n      - Environmental compliance\\n\\n      ### 3. Regional Trade Networks\\n      We're seeing a shift towards regional trade networks that offer:\\n      - Reduced transportation costs\\n      - Faster delivery times\\n      - Greater supply chain resilience\\n      - Enhanced market access\\n\\n      ## Adapting Your Business Strategy\\n\\n      To thrive in this evolving landscape, businesses should:\\n\\n      1. Invest in digital infrastructure\\n      2. Develop sustainable practices\\n      3. Diversify supply chains\\n      4. Build regional partnerships\\n\\n      ## Our Solutions\\n\\n      At CommercePro, we offer comprehensive solutions to help you navigate these changes:\\n\\n      - Advanced tracking systems for supply chain visibility\\n      - Sustainable packaging options\\n      - Regional distribution networks\\n      - Digital documentation management\\n\\n      Contact us to learn how we can help optimize your international trade operations.\\n    \",\n        content_ar: undefined,\n        author: 'Sarah Chen',\n        authorTitle: 'International Trade Specialist',\n        authorImage: 'https://images.pexels.com/photos/2381069/pexels-photo-2381069.jpeg',\n        coverImage: 'https://images.pexels.com/photos/4481259/pexels-photo-4481259.jpeg',\n        category: 'International Trade',\n        tags: [\n            'Supply Chain',\n            'Technology',\n            'Sustainability',\n            'Trade'\n        ],\n        publishedAt: '2025-03-01T08:00:00Z',\n        readTime: '8 min read',\n        featured: false\n    },\n    {\n        id: '2',\n        title: 'Mastering Export Documentation: A Comprehensive Guide',\n        title_ar: undefined,\n        slug: 'export-documentation-guide',\n        excerpt: 'Essential knowledge about export documentation requirements and best practices for international trade success.',\n        excerpt_ar: undefined,\n        content: \"\\n      Proper documentation is the foundation of successful international trade. This comprehensive guide will help you understand and manage export documentation effectively.\\n\\n      ## Essential Export Documents\\n\\n      ### 1. Commercial Documents\\n      - Commercial Invoice\\n      - Packing List\\n      - Certificate of Origin\\n      - Bill of Lading/Airway Bill\\n\\n      ### 2. Regulatory Documents\\n      - Export License\\n      - Export Declaration\\n      - Safety Certificates\\n      - Health Certificates\\n\\n      ### 3. Financial Documents\\n      - Letter of Credit\\n      - Bank Draft\\n      - Insurance Certificate\\n\\n      ## Common Documentation Challenges\\n\\n      1. Incomplete Information\\n      2. Inconsistent Data\\n      3. Delayed Processing\\n      4. Regulatory Compliance\\n\\n      ## Best Practices\\n\\n      - Maintain accurate records\\n      - Use digital documentation systems\\n      - Regular staff training\\n      - Work with experienced partners\\n\\n      ## Our Documentation Services\\n\\n      CommercePro offers comprehensive documentation support:\\n      - Digital document management\\n      - Compliance checking\\n      - Expert consultation\\n      - Training programs\\n\\n      Contact our team to streamline your export documentation process.\\n    \",\n        content_ar: undefined,\n        author: 'Michael Rodriguez',\n        authorTitle: 'Export Documentation Specialist',\n        authorImage: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',\n        coverImage: 'https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg',\n        category: 'Export',\n        tags: [\n            'Documentation',\n            'Compliance',\n            'International Trade'\n        ],\n        publishedAt: '2025-02-28T10:00:00Z',\n        readTime: '10 min read',\n        featured: false\n    },\n    {\n        id: '3',\n        title: 'Smart Manufacturing: Industry 4.0 Implementation Guide',\n        title_ar: undefined,\n        slug: 'smart-manufacturing-guide',\n        excerpt: 'A practical guide to implementing Industry 4.0 technologies in your manufacturing operations.',\n        excerpt_ar: undefined,\n        content: \"\\n      Smart manufacturing is revolutionizing production processes. Learn how to implement Industry 4.0 technologies effectively in your operations.\\n\\n      ## Key Components of Smart Manufacturing\\n\\n      ### 1. IoT Integration\\n      - Real-time monitoring\\n      - Predictive maintenance\\n      - Asset tracking\\n      - Quality control\\n\\n      ### 2. Data Analytics\\n      - Production optimization\\n      - Quality prediction\\n      - Resource management\\n      - Cost reduction\\n\\n      ### 3. Automation Systems\\n      - Robotic process automation\\n      - Automated quality control\\n      - Smart inventory management\\n      - Autonomous logistics\\n\\n      ## Implementation Strategy\\n\\n      1. Assessment Phase\\n      2. Technology Selection\\n      3. Pilot Implementation\\n      4. Scale-up Process\\n\\n      ## Success Stories\\n\\n      Learn how our clients achieved success with our smart manufacturing solutions:\\n\\n      ### Case Study: TechPro Manufacturing\\n      - 40% reduction in downtime\\n      - 25% increase in productivity\\n      - 15% cost savings\\n      - ROI within 18 months\\n\\n      ## Our Smart Manufacturing Solutions\\n\\n      Explore our range of Industry 4.0 products:\\n      - IoT Sensor Systems\\n      - Manufacturing Analytics Platform\\n      - Automated Quality Control\\n      - Smart Inventory Management\\n\\n      Contact us to start your smart manufacturing journey.\\n    \",\n        content_ar: undefined,\n        author: 'David Zhang',\n        authorTitle: 'Manufacturing Technology Expert',\n        authorImage: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg',\n        coverImage: 'https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg',\n        category: 'Manufacturing',\n        tags: [\n            'Industry 4.0',\n            'Technology',\n            'Automation'\n        ],\n        publishedAt: '2025-02-25T09:00:00Z',\n        readTime: '12 min read',\n        featured: false\n    },\n    {\n        id: '4',\n        title: 'Upcoming Trade Shows and Events 2025',\n        title_ar: undefined,\n        slug: 'trade-shows-2025',\n        excerpt: 'Mark your calendar for the most important international trade shows and industry events in 2025.',\n        excerpt_ar: undefined,\n        content: \"\\n      Stay informed about upcoming trade shows and industry events where you can network, learn about new technologies, and explore business opportunities.\\n\\n      ## Q1 2025 Events\\n\\n      ### International Manufacturing Expo\\n      - Date: January 15-17, 2025\\n      - Location: Singapore\\n      - Focus: Smart manufacturing technologies\\n      - CommercePro Booth: Hall A, Stand 45\\n\\n      ### Global Trade Summit\\n      - Date: March 8-10, 2025\\n      - Location: Dubai, UAE\\n      - Focus: International trade policies\\n      - Special Session: \\\"Future of Digital Trade\\\"\\n\\n      ## Q2 2025 Events\\n\\n      ### European Industry Fair\\n      - Date: May 20-23, 2025\\n      - Location: Frankfurt, Germany\\n      - Focus: Industrial automation\\n      - Product Launch: New IoT Sensor System\\n\\n      ## Meet Us There\\n\\n      Visit our booth at these events to:\\n      - See live product demos\\n      - Meet our experts\\n      - Get exclusive offers\\n      - Network with industry leaders\\n\\n      ## Can't Attend?\\n\\n      We've got you covered:\\n      - Virtual booth tours\\n      - Online product demonstrations\\n      - Digital networking sessions\\n      - Post-event content access\\n\\n      Contact us to schedule a meeting at any of these events.\\n    \",\n        content_ar: undefined,\n        author: 'Emma Thompson',\n        authorTitle: 'Events Coordinator',\n        authorImage: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg',\n        coverImage: 'https://images.pexels.com/photos/2774556/pexels-photo-2774556.jpeg',\n        category: 'Events',\n        tags: [\n            'Trade Shows',\n            'Networking',\n            'Industry Events'\n        ],\n        publishedAt: '2025-02-20T10:00:00Z',\n        readTime: '6 min read',\n        featured: false\n    }\n];\nconst blogCategories = [\n    {\n        id: 'international-trade',\n        name: 'International Trade',\n        description: 'Insights and guides on global trade practices'\n    },\n    {\n        id: 'export',\n        name: 'Export',\n        description: 'Expert advice on export procedures and documentation'\n    },\n    {\n        id: 'manufacturing',\n        name: 'Manufacturing',\n        description: 'Latest trends in industrial manufacturing'\n    },\n    {\n        id: 'logistics',\n        name: 'Logistics',\n        description: 'Supply chain and logistics management'\n    },\n    {\n        id: 'events',\n        name: 'Events',\n        description: 'Upcoming trade shows and industry events'\n    },\n    {\n        id: 'market-insights',\n        name: 'Market Insights',\n        description: 'Analysis of market trends and opportunities'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/blogPosts.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/clearanceItems.ts":
/*!************************************!*\
  !*** ./src/data/clearanceItems.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearanceItems: () => (/* binding */ clearanceItems)\n/* harmony export */ });\nconst clearanceItems = [\n    {\n        id: '1',\n        name: 'Bulk Electronics Components',\n        description: 'Mixed lot of electronic components including resistors, capacitors, and LED lights. Perfect for manufacturers and electronics workshops.',\n        originalPrice: 5000,\n        clearancePrice: 2000,\n        minOrder: 1000,\n        availableQuantity: 50000,\n        image: 'https://images.pexels.com/photos/163100/circuit-circuit-board-resistor-computer-163100.jpeg',\n        category: 'Electronics',\n        condition: 'new',\n        location: 'Central Warehouse'\n    },\n    {\n        id: '2',\n        name: 'Industrial Packaging Materials',\n        description: 'Bulk lot of industrial-grade cardboard boxes, bubble wrap, and packing tape. Ideal for shipping and logistics companies.',\n        originalPrice: 3000,\n        clearancePrice: 1200,\n        minOrder: 500,\n        availableQuantity: 10000,\n        image: 'https://images.pexels.com/photos/4484078/pexels-photo-4484078.jpeg',\n        category: 'Packaging',\n        condition: 'new',\n        location: 'South Distribution Center'\n    },\n    {\n        id: '3',\n        name: 'Office Furniture Set',\n        description: 'Bulk lot of modern office furniture including desks, chairs, and filing cabinets. Perfect for office renovations or new setups.',\n        originalPrice: 15000,\n        clearancePrice: 6000,\n        minOrder: 50,\n        availableQuantity: 200,\n        image: 'https://images.pexels.com/photos/1957478/pexels-photo-1957478.jpeg',\n        category: 'Furniture',\n        condition: 'like-new',\n        location: 'East Warehouse'\n    },\n    {\n        id: '4',\n        name: 'Industrial Safety Equipment',\n        description: 'Bulk lot of safety gear including helmets, vests, and goggles. Essential for construction and industrial operations.',\n        originalPrice: 8000,\n        clearancePrice: 3500,\n        minOrder: 100,\n        availableQuantity: 1000,\n        image: 'https://images.pexels.com/photos/1474993/pexels-photo-1474993.jpeg',\n        category: 'Safety',\n        condition: 'new',\n        location: 'West Distribution Center'\n    },\n    {\n        id: '5',\n        name: 'Networking Equipment',\n        description: 'Bulk lot of networking equipment including routers, switches, and cables. Suitable for IT infrastructure upgrades.',\n        originalPrice: 12000,\n        clearancePrice: 4800,\n        minOrder: 50,\n        availableQuantity: 500,\n        image: 'https://images.pexels.com/photos/159304/network-cable-ethernet-computer-159304.jpeg',\n        category: 'IT Equipment',\n        condition: 'new',\n        location: 'North Warehouse'\n    },\n    {\n        id: '6',\n        name: 'Manufacturing Tools',\n        description: 'Bulk lot of industrial tools including power tools, hand tools, and measuring instruments. Essential for manufacturing facilities.',\n        originalPrice: 10000,\n        clearancePrice: 4000,\n        minOrder: 100,\n        availableQuantity: 1000,\n        image: 'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg',\n        category: 'Tools',\n        condition: 'new',\n        location: 'Central Warehouse'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/clearanceItems.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/services.ts":
/*!******************************!*\
  !*** ./src/data/services.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   services: () => (/* binding */ services)\n/* harmony export */ });\nconst services = [\n    {\n        id: 'inspection',\n        name: 'Inspection Services',\n        name_ar: 'خدمات الفحص',\n        slug: 'inspection',\n        description: 'Comprehensive quality control and product inspection services at every stage of your supply chain.',\n        description_ar: 'خدمات شاملة لمراقبة الجودة وفحص المنتجات في كل مرحلة من مراحل سلسلة التوريد الخاصة بك.',\n        icon: 'Search',\n        features: [\n            'Pre-shipment quality inspections',\n            'During production checks',\n            'Container loading supervision',\n            'Factory audits',\n            'Product testing and verification',\n            'Detailed inspection reports with photos',\n            'Quality control consulting'\n        ],\n        features_ar: [\n            'فحص الجودة قبل الشحن',\n            'فحوصات أثناء الإنتاج',\n            'الإشراف على تحميل الحاويات',\n            'تدقيق المصانع',\n            'اختبار المنتجات والتحقق منها',\n            'تقارير فحص مفصلة مع صور',\n            'استشارات مراقبة الجودة'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'storage',\n        name: 'Storage Solutions',\n        name_ar: 'حلول التخزين',\n        slug: 'storage',\n        description: 'Secure, climate-controlled warehousing with advanced inventory management systems.',\n        description_ar: 'مستودعات آمنة ومتحكم في مناخها مع أنظمة متقدمة لإدارة المخزون.',\n        icon: 'Package',\n        features: [\n            'Climate-controlled facilities',\n            'Advanced security systems',\n            'Real-time inventory tracking',\n            'Short and long-term storage options',\n            'Fulfillment services',\n            'Cross-docking capabilities',\n            'Inventory management software'\n        ],\n        features_ar: [\n            'مرافق متحكم في مناخها',\n            'أنظمة أمان متقدمة',\n            'تتبع المخزون في الوقت الفعلي',\n            'خيارات تخزين قصيرة وطويلة الأجل',\n            'خدمات الوفاء بالطلبات',\n            'قدرات النقل المتقاطع',\n            'برامج إدارة المخزون'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'shipping',\n        name: 'Shipping & Logistics',\n        name_ar: 'الشحن والخدمات اللوجستية',\n        slug: 'shipping',\n        description: 'End-to-end shipping solutions including air freight, sea shipping, and door-to-door delivery.',\n        description_ar: 'حلول شحن متكاملة تشمل الشحن الجوي والبحري والتوصيل من الباب إلى الباب.',\n        icon: 'Truck',\n        features: [\n            'International air freight',\n            'Ocean freight services',\n            'Door-to-door delivery',\n            'Customs clearance',\n            'Cargo insurance',\n            'Track and trace systems',\n            'Specialized handling'\n        ],\n        features_ar: [\n            'الشحن الجوي الدولي',\n            'خدمات الشحن البحري',\n            'التوصيل من الباب إلى الباب',\n            'التخليص الجمركي',\n            'تأمين البضائع',\n            'أنظمة التتبع والتعقب',\n            'المناولة المتخصصة'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'order-management',\n        name: 'Order Management',\n        name_ar: 'إدارة الطلبات',\n        slug: 'order-management',\n        description: 'Streamlined wholesale and bulk order processing with dedicated support and competitive pricing.',\n        description_ar: 'معالجة مبسطة لطلبات الجملة والطلبات الكبيرة مع دعم مخصص وأسعار تنافسية.',\n        icon: 'ClipboardList',\n        features: [\n            'Volume-based pricing',\n            'Dedicated account management',\n            'Customized catalogs',\n            'Flexible payment terms',\n            'Priority support',\n            'Order tracking system',\n            'Bulk order processing'\n        ],\n        features_ar: [\n            'تسعير على أساس الحجم',\n            'إدارة حساب مخصصة',\n            'كتالوجات مخصصة',\n            'شروط دفع مرنة',\n            'دعم ذو أولوية',\n            'نظام تتبع الطلبات',\n            'معالجة الطلبات الكبيرة'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'certification',\n        name: 'Product Certification',\n        name_ar: 'شهادات المنتجات',\n        slug: 'certification',\n        description: 'Expert assistance with product certification and regulatory compliance requirements.',\n        description_ar: 'مساعدة خبيرة في شهادات المنتجات ومتطلبات الامتثال التنظيمية.',\n        icon: 'FileCheck',\n        features: [\n            'CE certification assistance',\n            'FDA compliance support',\n            'ISO certification guidance',\n            'Product testing coordination',\n            'Documentation preparation',\n            'Regulatory consulting',\n            'Compliance monitoring'\n        ],\n        features_ar: [\n            'المساعدة في شهادة CE',\n            'دعم الامتثال لمعايير FDA',\n            'توجيه شهادة ISO',\n            'تنسيق اختبار المنتجات',\n            'إعداد الوثائق',\n            'الاستشارات التنظيمية',\n            'مراقبة الامتثال'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'consulting',\n        name: 'Business Consulting',\n        name_ar: 'الاستشارات التجارية',\n        slug: 'consulting',\n        description: 'Strategic consulting services to optimize your operations and expand market presence.',\n        description_ar: 'خدمات استشارية استراتيجية لتحسين عملياتك وتوسيع تواجدك في السوق.',\n        icon: 'Users',\n        features: [\n            'Supply chain optimization',\n            'Market entry strategies',\n            'Operational efficiency',\n            'Cost reduction analysis',\n            'Vendor management',\n            'Risk assessment',\n            'Growth planning'\n        ],\n        features_ar: [\n            'تحسين سلسلة التوريد',\n            'استراتيجيات دخول السوق',\n            'كفاءة العمليات',\n            'تحليل خفض التكاليف',\n            'إدارة الموردين',\n            'تقييم المخاطر',\n            'تخطيط النمو'\n        ],\n        createdAt: new Date().toISOString()\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/services.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4730ce040dfd\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcZWNvbW1lcmNlcHJvXFxzcmNcXGluZGV4LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQ3MzBjZTA0MGRmZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/index.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _rateLimiter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rateLimiter */ \"(app-pages-browser)/./src/lib/rateLimiter.ts\");\n\n\n// إنشاء نسخة من Axios مع الإعدادات الافتراضية\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"/api\" || 0,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    timeout: 10000\n});\n// إضافة معترض للطلبات لتنفيذ CSRF Protection و Rate Limiting\napi.interceptors.request.use(async (config)=>{\n    // تنفيذ Rate Limiting\n    if (!_rateLimiter__WEBPACK_IMPORTED_MODULE_0__.defaultRateLimiter.isAllowed()) {\n        throw new Error('Rate limit exceeded. Please try again later.');\n    }\n    // تسجيل الطلب الجديد\n    _rateLimiter__WEBPACK_IMPORTED_MODULE_0__.defaultRateLimiter.logRequest();\n    // إضافة CSRF token إذا كان متاحًا\n    if (typeof document !== 'undefined') {\n        var _document_querySelector;\n        const csrfToken = (_document_querySelector = document.querySelector('meta[name=\"csrf-token\"]')) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.getAttribute('content');\n        if (csrfToken && config.headers) {\n            config.headers['X-CSRF-Token'] = csrfToken;\n        }\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// إضافة معترض للاستجابات للتعامل مع الأخطاء\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    // التعامل مع أخطاء الاستجابة\n    if (error.response) {\n        // خطأ من الخادم\n        const status = error.response.status;\n        if (status === 401) {\n            // غير مصرح - تسجيل الخروج\n            console.error('Unauthorized access. Please login again.');\n        // يمكن استدعاء وظيفة تسجيل الخروج هنا\n        } else if (status === 403) {\n            // ممنوع\n            console.error('Access forbidden.');\n        } else if (status === 429) {\n            // تجاوز حد الطلبات\n            console.error('Rate limit exceeded. Please try again later.');\n        } else if (status >= 500) {\n            // خطأ في الخادم\n            console.error('Server error. Please try again later.');\n        }\n    } else if (error.request) {\n        // لم يتم استلام استجابة\n        console.error('No response received from server.');\n    } else {\n        // خطأ في إعداد الطلب\n        console.error('Error setting up the request:', error.message);\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localCache.ts":
/*!*******************************!*\
  !*** ./src/lib/localCache.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocalCache: () => (/* binding */ LocalCache),\n/* harmony export */   defaultCache: () => (/* binding */ defaultCache)\n/* harmony export */ });\n/* harmony import */ var _encryption__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./encryption */ \"(app-pages-browser)/./src/lib/encryption.ts\");\n// تنفيذ التخزين المؤقت المحلي للبيانات\n\nclass LocalCache {\n    // إنشاء مفتاح التخزين\n    createKey(key) {\n        return \"\".concat(this.prefix, \":\").concat(key);\n    }\n    // تخزين البيانات في التخزين المحلي مع التشفير\n    set(key, data) {\n        let ttlSeconds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3600, userId = arguments.length > 3 ? arguments[3] : void 0;\n        try {\n            const cacheKey = this.createKey(key);\n            const item = {\n                data,\n                expiry: Date.now() + ttlSeconds * 1000\n            };\n            // استخدام التشفير لتخزين البيانات\n            const secretKey = (0,_encryption__WEBPACK_IMPORTED_MODULE_0__.getUserSecretKey)(userId);\n            const encryptedData = (0,_encryption__WEBPACK_IMPORTED_MODULE_0__.encrypt)(item, secretKey);\n            localStorage.setItem(cacheKey, encryptedData);\n        } catch (error) {\n            console.error('Error setting cache item:', error);\n        }\n    }\n    // استرداد البيانات من التخزين المحلي مع فك التشفير\n    get(key, userId) {\n        try {\n            const cacheKey = this.createKey(key);\n            const encryptedItem = localStorage.getItem(cacheKey);\n            if (!encryptedItem) {\n                return null;\n            }\n            // فك تشفير البيانات\n            const secretKey = (0,_encryption__WEBPACK_IMPORTED_MODULE_0__.getUserSecretKey)(userId);\n            const parsedItem = (0,_encryption__WEBPACK_IMPORTED_MODULE_0__.decrypt)(encryptedItem, secretKey);\n            if (!parsedItem) {\n                this.remove(key);\n                return null;\n            }\n            // التحقق من صلاحية البيانات\n            if (parsedItem.expiry < Date.now()) {\n                this.remove(key);\n                return null;\n            }\n            return parsedItem.data;\n        } catch (error) {\n            console.error('Error getting cache item:', error);\n            return null;\n        }\n    }\n    // إزالة عنصر من التخزين المؤقت\n    remove(key) {\n        try {\n            const cacheKey = this.createKey(key);\n            localStorage.removeItem(cacheKey);\n        } catch (error) {\n            console.error('Error removing cache item:', error);\n        }\n    }\n    // مسح جميع العناصر المخزنة مؤقتًا\n    clear() {\n        try {\n            const keys = Object.keys(localStorage);\n            for (const key of keys){\n                if (key.startsWith(this.prefix)) {\n                    localStorage.removeItem(key);\n                }\n            }\n        } catch (error) {\n            console.error('Error clearing cache:', error);\n        }\n    }\n    // مسح العناصر منتهية الصلاحية\n    clearExpired() {\n        try {\n            const keys = Object.keys(localStorage);\n            const now = Date.now();\n            for (const key of keys){\n                if (key.startsWith(this.prefix)) {\n                    const item = localStorage.getItem(key);\n                    if (item) {\n                        try {\n                            const parsedItem = JSON.parse(item);\n                            if (parsedItem.expiry && parsedItem.expiry < now) {\n                                localStorage.removeItem(key);\n                            }\n                        } catch (e) {\n                        // تجاهل الأخطاء في تحليل العناصر غير الصالحة\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error clearing expired cache items:', error);\n        }\n    }\n    constructor(prefix = 'app-cache'){\n        this.prefix = prefix;\n    }\n}\n// إنشاء نسخة افتراضية من التخزين المؤقت المحلي\nconst defaultCache = new LocalCache();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localCache.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/rateLimiter.ts":
/*!********************************!*\
  !*** ./src/lib/rateLimiter.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RateLimiter: () => (/* binding */ RateLimiter),\n/* harmony export */   defaultRateLimiter: () => (/* binding */ defaultRateLimiter)\n/* harmony export */ });\n// تنفيذ Rate Limiting على جانب العميل\n// هذا مفيد لمنع المستخدمين من إرسال طلبات متعددة في فترة زمنية قصيرة\nclass RateLimiter {\n    // تحميل بيانات التخزين من localStorage\n    loadStorage() {\n        if (true) {\n            try {\n                const data = localStorage.getItem(this.storageKey);\n                if (data) {\n                    return JSON.parse(data);\n                }\n            } catch (error) {\n                console.error('Error loading rate limiter data:', error);\n            }\n        }\n        return {\n            timestamps: [],\n            lastReset: Date.now()\n        };\n    }\n    // حفظ بيانات التخزين في localStorage\n    saveStorage() {\n        if (true) {\n            try {\n                localStorage.setItem(this.storageKey, JSON.stringify(this.storage));\n            } catch (error) {\n                console.error('Error saving rate limiter data:', error);\n            }\n        }\n    }\n    // تنظيف الطوابع الزمنية القديمة\n    cleanup() {\n        const now = Date.now();\n        const windowStart = now - this.options.windowMs;\n        // إزالة الطوابع الزمنية القديمة خارج النافذة الزمنية\n        this.storage.timestamps = this.storage.timestamps.filter((timestamp)=>timestamp >= windowStart);\n        this.saveStorage();\n    }\n    // التحقق مما إذا كان الطلب مسموحًا به\n    isAllowed() {\n        this.cleanup();\n        return this.storage.timestamps.length < this.options.maxRequests;\n    }\n    // تسجيل طلب جديد\n    logRequest() {\n        this.cleanup();\n        if (this.storage.timestamps.length >= this.options.maxRequests) {\n            return false;\n        }\n        this.storage.timestamps.push(Date.now());\n        this.saveStorage();\n        return true;\n    }\n    // الحصول على عدد الطلبات المتبقية\n    getRemainingRequests() {\n        this.cleanup();\n        return Math.max(0, this.options.maxRequests - this.storage.timestamps.length);\n    }\n    // الحصول على الوقت المتبقي حتى إعادة تعيين العداد\n    getResetTime() {\n        const now = Date.now();\n        if (this.storage.timestamps.length === 0) {\n            return 0;\n        }\n        const oldestTimestamp = Math.min(...this.storage.timestamps);\n        return Math.max(0, oldestTimestamp + this.options.windowMs - now);\n    }\n    constructor(options){\n        this.options = {\n            maxRequests: options.maxRequests || 60,\n            windowMs: options.windowMs || 60000,\n            storageKey: options.storageKey || 'rate-limiter'\n        };\n        this.storageKey = this.options.storageKey;\n        this.storage = this.loadStorage();\n    }\n}\n// إنشاء نسخة افتراضية من Rate Limiter\nconst defaultRateLimiter = new RateLimiter({\n    maxRequests: 50,\n    windowMs: 60000,\n    storageKey: 'app-rate-limiter'\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/rateLimiter.ts\n"));

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blocale%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CRootLayout.tsx%22%2C%22ids%22%3A%5B%22RootLayout%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cecommercepro%5C%5Csrc%5C%5Cindex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);