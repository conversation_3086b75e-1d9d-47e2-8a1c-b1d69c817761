"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_sections_Newsletter_tsx",{

/***/ "(app-pages-browser)/./src/components/sections/Newsletter.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Newsletter.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,MailIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,MailIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Newsletter = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function Newsletter() {\n    _s();\n    const { language, t } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubscribed, setIsSubscribed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(t('newsletter.errors.emailRequired', 'Email is required'));\n            return;\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(t('newsletter.errors.emailInvalid', 'Please enter a valid email address'));\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            setIsSubscribed(true);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(t('newsletter.success', 'Successfully subscribed to newsletter!'));\n            setEmail('');\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(t('newsletter.errors.general', 'Something went wrong. Please try again.'));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isSubscribed) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-gradient-to-r from-blue-600 to-purple-600\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"text-center text-white\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-16 h-16 mx-auto mb-4 text-green-300\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: t('newsletter.thankYou', 'Thank You!')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl opacity-90\",\n                            children: t('newsletter.confirmationMessage', 'You\\'ve been successfully subscribed to our newsletter.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gradient-to-r from-blue-600 to-purple-600\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"max-w-4xl mx-auto text-center text-white\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.6\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_MailIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-8 h-8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                children: t('newsletter.title', 'Stay Updated')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl opacity-90 max-w-2xl mx-auto\",\n                                children: t('newsletter.description', 'Subscribe to our newsletter and be the first to know about new products, special offers, and exclusive deals.')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.form, {\n                        onSubmit: handleSubmit,\n                        className: \"max-w-md mx-auto\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"email\",\n                                        placeholder: t('newsletter.emailPlaceholder', 'Enter your email address'),\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 rounded-xl border-0 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50\",\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"px-8 py-3 bg-white text-blue-600 hover:bg-gray-100 rounded-xl font-semibold transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 21\n                                            }, this),\n                                            t('newsletter.subscribing', 'Subscribing...')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 19\n                                    }, this) : t('newsletter.subscribe', 'Subscribe')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                        className: \"text-sm opacity-75 mt-4\",\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 0.75\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        children: t('newsletter.privacy', 'We respect your privacy. Unsubscribe at any time.')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.7\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"\\uD83D\\uDCE7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: t('newsletter.benefits.updates', 'Latest Updates')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: t('newsletter.benefits.updatesDesc', 'Get notified about new features')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"\\uD83C\\uDF81\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: t('newsletter.benefits.offers', 'Exclusive Offers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: t('newsletter.benefits.offersDesc', 'Special discounts for subscribers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: t('newsletter.benefits.early', 'Early Access')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-75\",\n                                        children: t('newsletter.benefits.earlyDesc', 'Be first to try new products')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Newsletter.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}, \"/NNgxnCfisOX29iPePBlviF7SV8=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore\n    ];\n})), \"/NNgxnCfisOX29iPePBlviF7SV8=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore\n    ];\n});\n_c1 = Newsletter;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Newsletter);\nvar _c, _c1;\n$RefreshReg$(_c, \"Newsletter$memo\");\n$RefreshReg$(_c1, \"Newsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Newsletter.tsx\n"));

/***/ })

});