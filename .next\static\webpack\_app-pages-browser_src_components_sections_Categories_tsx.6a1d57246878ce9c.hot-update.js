"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_sections_Categories_tsx",{

/***/ "(app-pages-browser)/./src/components/sections/Categories.tsx":
/*!************************************************!*\
  !*** ./src/components/sections/Categories.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst categories = [\n    {\n        id: 'electronics',\n        name: {\n            en: 'Electronics',\n            ar: 'إلكترونيات'\n        },\n        description: {\n            en: 'Latest gadgets and devices',\n            ar: 'أحدث الأجهزة والتقنيات'\n        },\n        image: '/images/categories/electronics.jpg',\n        href: '/shop?category=electronics',\n        color: 'from-blue-500 to-cyan-500'\n    },\n    {\n        id: 'fashion',\n        name: {\n            en: 'Fashion',\n            ar: 'أزياء'\n        },\n        description: {\n            en: 'Trendy clothing and accessories',\n            ar: 'ملابس وإكسسوارات عصرية'\n        },\n        image: '/images/categories/fashion.jpg',\n        href: '/shop?category=fashion',\n        color: 'from-pink-500 to-rose-500'\n    },\n    {\n        id: 'home',\n        name: {\n            en: 'Home & Garden',\n            ar: 'منزل وحديقة'\n        },\n        description: {\n            en: 'Everything for your home',\n            ar: 'كل ما تحتاجه لمنزلك'\n        },\n        image: '/images/categories/home.jpg',\n        href: '/shop?category=home',\n        color: 'from-green-500 to-emerald-500'\n    },\n    {\n        id: 'sports',\n        name: {\n            en: 'Sports & Fitness',\n            ar: 'رياضة ولياقة'\n        },\n        description: {\n            en: 'Gear for active lifestyle',\n            ar: 'معدات للحياة النشطة'\n        },\n        image: '/images/categories/sports.jpg',\n        href: '/shop?category=sports',\n        color: 'from-orange-500 to-amber-500'\n    },\n    {\n        id: 'beauty',\n        name: {\n            en: 'Beauty & Health',\n            ar: 'جمال وصحة'\n        },\n        description: {\n            en: 'Care for your wellbeing',\n            ar: 'اعتني بصحتك وجمالك'\n        },\n        image: '/images/categories/beauty.jpg',\n        href: '/shop?category=beauty',\n        color: 'from-purple-500 to-violet-500'\n    },\n    {\n        id: 'automotive',\n        name: {\n            en: 'Automotive',\n            ar: 'سيارات'\n        },\n        description: {\n            en: 'Car parts and accessories',\n            ar: 'قطع غيار وإكسسوارات السيارات'\n        },\n        image: '/images/categories/automotive.jpg',\n        href: '/shop?category=automotive',\n        color: 'from-gray-600 to-slate-600'\n    }\n];\nconst Categories = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function Categories() {\n    _s();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_4__.useLanguageStore)();\n    const isRTL = language === 'ar';\n    // Simple fallback translation function for now\n    const t = (key, fallback)=>fallback;\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: 'easeOut'\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('categories.title', 'Shop by Category')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t('categories.description', 'Explore our wide range of product categories and find exactly what you\\'re looking for.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: category.href,\n                                className: \"group block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-48 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: category.image,\n                                                    alt: category.name[language],\n                                                    fill: true,\n                                                    className: \"object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                    sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br \".concat(category.color, \" opacity-20 group-hover:opacity-30 transition-opacity duration-300\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block px-3 py-1 bg-white/90 backdrop-blur-sm text-gray-800 text-sm font-medium rounded-full\",\n                                                        children: category.name[language]\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300\",\n                                                    children: category.name[language]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: category.description[language]\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-blue-600 font-medium group-hover:text-blue-700 transition-colors duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mr-2\",\n                                                            children: t('categories.shopNow', 'Shop Now')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 \".concat(isRTL ? 'rotate-180 group-hover:-translate-x-1' : '')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 border-2 border-transparent group-hover:border-blue-200 rounded-2xl transition-colors duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, category.id, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"text-center mt-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: t('categories.bottomText', 'Can\\'t find what you\\'re looking for?')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop\",\n                            className: \"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300\",\n                            children: [\n                                t('categories.browseAll', 'Browse All Products'),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRTL ? 'rotate-180' : '')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\Categories.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}, \"U9lZRyUdG8HIwpHFfafb+brsRy0=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_4__.useLanguageStore\n    ];\n})), \"U9lZRyUdG8HIwpHFfafb+brsRy0=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_4__.useLanguageStore\n    ];\n});\n_c1 = Categories;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Categories);\nvar _c, _c1;\n$RefreshReg$(_c, \"Categories$memo\");\n$RefreshReg$(_c1, \"Categories\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Categories.tsx\n"));

/***/ })

});