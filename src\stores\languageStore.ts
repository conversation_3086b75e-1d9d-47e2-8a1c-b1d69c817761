import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface LanguageState {
  language: 'en' | 'ar';
  direction: 'ltr' | 'rtl';
  initialize: () => void;
  setLanguage: (language: 'en' | 'ar') => void;
  t: (key: string, fallback?: string) => string;
}

// Simple translation dictionary
const translations = {
  en: {
    'hero.badge': 'Enterprise E-commerce Platform',
    'hero.title': 'Build Your',
    'hero.titleHighlight': 'Dream Store',
    'hero.titleEnd': 'Today',
    'hero.description': 'Professional e-commerce platform with advanced security, payment integration, and comprehensive admin dashboard. Built with modern technologies for optimal performance.',
    'hero.shopNow': 'Shop Now',
    'hero.watchDemo': 'Watch Demo',
    'categories.title': 'Shop by Category',
    'categories.description': 'Explore our wide range of product categories and find exactly what you\'re looking for.',
    'categories.shopNow': 'Shop Now',
    'categories.browseAll': 'Browse All Products',
    'featuredProducts.title': 'Featured Products',
    'featuredProducts.description': 'Discover our handpicked selection of premium products, carefully chosen for their quality and value.',
    'featuredProducts.viewAll': 'View All Products',
    'services.title': 'Why Choose Us',
    'services.description': 'We provide exceptional service and support to ensure your shopping experience is seamless and enjoyable.',
    'services.cta.title': 'Need Help?',
    'services.cta.description': 'Our customer service team is here to help you with any questions or concerns you may have.',
    'services.cta.button': 'Contact Support',
    'testimonials.title': 'What Our Customers Say',
    'testimonials.description': 'Don\'t just take our word for it. Here\'s what our satisfied customers have to say about their experience.',
    'newsletter.title': 'Stay Updated',
    'newsletter.description': 'Subscribe to our newsletter and be the first to know about new products, special offers, and exclusive deals.',
    'newsletter.subscribe': 'Subscribe',
    'newsletter.subscribing': 'Subscribing...',
    'newsletter.emailPlaceholder': 'Enter your email address',
    'newsletter.privacy': 'We respect your privacy. Unsubscribe at any time.',
  },
  ar: {
    'hero.badge': 'منصة تجارة إلكترونية متقدمة',
    'hero.title': 'ابني',
    'hero.titleHighlight': 'متجرك المثالي',
    'hero.titleEnd': 'اليوم',
    'hero.description': 'منصة تجارة إلكترونية احترافية مع أمان متقدم وتكامل المدفوعات ولوحة تحكم شاملة. مبنية بتقنيات حديثة للأداء الأمثل.',
    'hero.shopNow': 'تسوق الآن',
    'hero.watchDemo': 'شاهد العرض',
    'categories.title': 'تسوق حسب الفئة',
    'categories.description': 'استكشف مجموعتنا الواسعة من فئات المنتجات واعثر على ما تبحث عنه بالضبط.',
    'categories.shopNow': 'تسوق الآن',
    'categories.browseAll': 'تصفح جميع المنتجات',
    'featuredProducts.title': 'المنتجات المميزة',
    'featuredProducts.description': 'اكتشف مجموعتنا المختارة بعناية من المنتجات المميزة، المختارة بعناية لجودتها وقيمتها.',
    'featuredProducts.viewAll': 'عرض جميع المنتجات',
    'services.title': 'لماذا تختارنا',
    'services.description': 'نحن نقدم خدمة ودعم استثنائيين لضمان أن تكون تجربة التسوق الخاصة بك سلسة وممتعة.',
    'services.cta.title': 'تحتاج مساعدة؟',
    'services.cta.description': 'فريق خدمة العملاء لدينا هنا لمساعدتك في أي أسئلة أو مخاوف قد تكون لديك.',
    'services.cta.button': 'اتصل بالدعم',
    'testimonials.title': 'ماذا يقول عملاؤنا',
    'testimonials.description': 'لا تأخذ كلامنا فقط. إليك ما يقوله عملاؤنا الراضون عن تجربتهم.',
    'newsletter.title': 'ابق على اطلاع',
    'newsletter.description': 'اشترك في نشرتنا الإخبارية وكن أول من يعرف عن المنتجات الجديدة والعروض الخاصة والصفقات الحصرية.',
    'newsletter.subscribe': 'اشترك',
    'newsletter.subscribing': 'جاري الاشتراك...',
    'newsletter.emailPlaceholder': 'أدخل عنوان بريدك الإلكتروني',
    'newsletter.privacy': 'نحن نحترم خصوصيتك. إلغاء الاشتراك في أي وقت.',
  }
};

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set, get) => ({
      language: 'en', // Default to English for better compatibility
      direction: 'ltr', // Default to LTR

      initialize: () => {
        console.log('Language store: initializing');
        // Language is already persisted, no additional initialization needed
      },

      setLanguage: (language) => set({
        language,
        direction: language === 'ar' ? 'rtl' : 'ltr',
      }),

      t: (key: string, fallback?: string) => {
        const { language } = get();
        const translation = translations[language]?.[key];
        return translation || fallback || key;
      },
    }),
    {
      name: 'language-storage',
    }
  )
);