import type { NextPage, GetStaticProps } from 'next';
import Head from 'next/head';
import { useTranslation } from '../translations';
import { useLanguage } from '../hooks/useLanguage';

// Components
import Layout from '../components/layout/Layout';
import Hero from '../components/sections/Hero';
import FeaturedProducts from '../components/sections/FeaturedProducts';
import FeaturedServices from '../components/sections/FeaturedServices';
import Categories from '../components/sections/Categories';
import Testimonials from '../components/sections/Testimonials';
import Newsletter from '../components/sections/Newsletter';

// Types
import type { Product } from '../types/product';
import type { Service } from '../types/service';
import type { Category } from '../data/categories';

interface HomePageProps {
  featuredProducts: Product[];
  featuredServices: Service[];
  categories: Category[];
  testimonials: any[];
}

const HomePage: NextPage<HomePageProps> = ({
  featuredProducts,
  featuredServices,
  categories,
  testimonials
}) => {
  const { t } = useTranslation();
  const { language, getLocalizedValue } = useLanguage();

  const pageTitle = getLocalizedValue(
    'Commerce Pro - Professional E-commerce Platform',
    'كومرس برو - منصة التجارة الإلكترونية المتخصصة'
  );

  const pageDescription = getLocalizedValue(
    'Discover quality products and professional services with Commerce Pro. Your trusted partner for online shopping and business solutions.',
    'اكتشف المنتجات عالية الجودة والخدمات المتخصصة مع كومرس برو. شريكك الموثوق للتسوق الإلكتروني والحلول التجارية.'
  );

  return (
    <>
      <Head>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="keywords" content="ecommerce, online shopping, products, services, تجارة إلكترونية, تسوق أونلاين, منتجات, خدمات" />
        
        {/* Open Graph */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://commercepro.com" />
        <meta property="og:image" content="https://commercepro.com/images/og-home.jpg" />
        
        {/* Twitter */}
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
        <meta name="twitter:image" content="https://commercepro.com/images/twitter-home.jpg" />
        
        {/* Canonical URL */}
        <link rel="canonical" href="https://commercepro.com" />
        
        {/* Alternate languages */}
        <link rel="alternate" hrefLang="ar" href="https://commercepro.com/ar" />
        <link rel="alternate" hrefLang="en" href="https://commercepro.com/en" />
        <link rel="alternate" hrefLang="x-default" href="https://commercepro.com" />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "Commerce Pro",
              "url": "https://commercepro.com",
              "description": pageDescription,
              "inLanguage": [
                {
                  "@type": "Language",
                  "name": "Arabic",
                  "alternateName": "ar"
                },
                {
                  "@type": "Language", 
                  "name": "English",
                  "alternateName": "en"
                }
              ],
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://commercepro.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </Head>

      <Layout>
        {/* Hero Section */}
        <Hero />

        {/* Featured Categories */}
        <Categories categories={categories} />

        {/* Featured Products */}
        <FeaturedProducts products={featuredProducts} />

        {/* Featured Services */}
        <FeaturedServices services={featuredServices} />

        {/* Testimonials */}
        <Testimonials testimonials={testimonials} />

        {/* Newsletter Signup */}
        <Newsletter />
      </Layout>
    </>
  );
};

export const getStaticProps: GetStaticProps<HomePageProps> = async ({ locale }) => {
  try {
    // In a real application, these would be API calls
    // For now, we'll return mock data
    
    const featuredProducts: Product[] = [];
    const featuredServices: Service[] = [];
    const categories: Category[] = [];
    const testimonials: any[] = [];

    return {
      props: {
        featuredProducts,
        featuredServices,
        categories,
        testimonials
      },
      revalidate: 60 * 15 // Revalidate every 15 minutes
    };
  } catch (error) {
    console.error('Error fetching home page data:', error);
    
    return {
      props: {
        featuredProducts: [],
        featuredServices: [],
        categories: [],
        testimonials: []
      },
      revalidate: 60 * 5 // Retry in 5 minutes on error
    };
  }
};

export default HomePage;
