"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_sections_FeaturedProducts_tsx"],{

/***/ "(app-pages-browser)/./src/components/product/ProductCard.tsx":
/*!************************************************!*\
  !*** ./src/components/product/ProductCard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCard: () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/EnhancedImage */ \"(app-pages-browser)/./src/components/ui/EnhancedImage.tsx\");\n/* harmony import */ var _stores_cartStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../stores/cartStore */ \"(app-pages-browser)/./src/stores/cartStore.ts\");\n/* harmony import */ var _stores_wishlistStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../stores/wishlistStore */ \"(app-pages-browser)/./src/stores/wishlistStore.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../translations */ \"(app-pages-browser)/./src/translations/index.ts\");\n/* harmony import */ var _ui_animations_HoverAnimation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../ui/animations/HoverAnimation */ \"(app-pages-browser)/./src/components/ui/animations/HoverAnimation.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductCard(param) {\n    let { product, index = 0, className = '', showQuickView = true, showAddToCart = true, showWishlist = true, onQuickView, onAddToCart, onToggleWishlist } = param;\n    var _product_rating;\n    _s();\n    const { t, currentLanguage } = (0,_translations__WEBPACK_IMPORTED_MODULE_10__.useTranslation)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore)();\n    const cartStore = (0,_stores_cartStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore)();\n    const wishlistStore = (0,_stores_wishlistStore__WEBPACK_IMPORTED_MODULE_7__.useWishlistStore)();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAddToCart = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n        if (onAddToCart) {\n            onAddToCart(product);\n        } else {\n            cartStore.addItem({\n                id: product.id,\n                name: product.name,\n                name_ar: product.name_ar,\n                price: product.price,\n                image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',\n                quantity: 1\n            });\n        }\n    };\n    const handleToggleWishlist = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n        if (onToggleWishlist) {\n            onToggleWishlist(product);\n        } else {\n            if (wishlistStore.isInWishlist(product.id)) {\n                wishlistStore.removeItem(product.id);\n            } else {\n                wishlistStore.addItem(product);\n            }\n        }\n    };\n    const handleQuickView = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (onQuickView) {\n            onQuickView(product);\n        }\n    };\n    // Fallback image if the product image fails to load\n    const fallbackImage = \"/images/product-placeholder-\".concat(isDarkMode ? 'dark' : 'light', \".svg\");\n    // Use the first product image or fallback if there are no images\n    const productImage = imageError || !product.images || product.images.length === 0 ? fallbackImage : product.images[0];\n    // تحديد ما إذا كان المنتج في المخزون\n    const isInStock = product.stock > 0;\n    // حساب نسبة الخصم إذا كان هناك سعر مقارنة\n    const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price ? Math.round((1 - product.price / product.compareAtPrice) * 100) : product.discount || 0;\n    var _product_rating_toFixed, _product_reviewCount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_animations_HoverAnimation__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n        animation: \"lift\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"group flex flex-col h-full overflow-hidden rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-slate-800\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop/\".concat(product.slug),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-48 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_5__.EnhancedImage, {\n                                    src: productImage,\n                                    alt: product.name,\n                                    fill: true,\n                                    objectFit: \"cover\",\n                                    progressive: true,\n                                    placeholder: \"shimmer\",\n                                    className: \"transition-transform duration-500 group-hover:scale-105\",\n                                    sizes: \"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw\",\n                                    priority: index < 4,\n                                    onError: ()=>setImageError(true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 flex flex-col gap-1 z-10\",\n                            children: [\n                                product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-blue-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? 'جديد' : 'New'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                discountPercentage > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-red-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? \"\".concat(discountPercentage, \"% خصم\") : \"\".concat(discountPercentage, \"% OFF\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                !isInStock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-gray-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-semibold bg-amber-500 text-white rounded-full shadow-sm\",\n                                    children: currentLanguage === 'ar' ? 'مميز' : 'Featured'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 right-2 flex flex-col gap-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            children: [\n                                showWishlist && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"icon\",\n                                    size: \"sm\",\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"p-1.5 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110\", wishlistStore.isInWishlist(product.id) ? \"bg-primary-500 text-white\" : \"bg-white text-slate-700 dark:bg-slate-700 dark:text-white\"),\n                                    onClick: handleToggleWishlist,\n                                    \"aria-label\": t('shop.addToWishlist'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"h-4 w-4\", wishlistStore.isInWishlist(product.id) && \"fill-current\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                showQuickView && onQuickView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"icon\",\n                                    size: \"sm\",\n                                    className: \"p-1.5 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110\",\n                                    onClick: handleQuickView,\n                                    \"aria-label\": t('shop.quickView'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 flex flex-col flex-grow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                product.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full\",\n                                    children: product.category\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-slate-500 dark:text-slate-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-400 \".concat(currentLanguage === 'ar' ? 'ml-1' : 'mr-1')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                (_product_rating_toFixed = (_product_rating = product.rating) === null || _product_rating === void 0 ? void 0 : _product_rating.toFixed(1)) !== null && _product_rating_toFixed !== void 0 ? _product_rating_toFixed : 'N/A',\n                                                \"(\",\n                                                (_product_reviewCount = product.reviewCount) !== null && _product_reviewCount !== void 0 ? _product_reviewCount : 0,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\".concat(currentLanguage, \"/shop/\").concat(product.slug),\n                            className: \"block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1\",\n                                children: currentLanguage === 'ar' ? product.name_ar || product.name : product.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm\",\n                            children: currentLanguage === 'ar' ? product.description_ar || product.description : product.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3 mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-baseline gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-slate-900 dark:text-white\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(product.price)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.compareAtPrice && product.compareAtPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-500 line-through\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatCurrency)(product.compareAtPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium\",\n                                    children: isInStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 dark:text-green-400\",\n                                        children: currentLanguage === 'ar' ? 'متوفر' : 'In Stock'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600 dark:text-red-400\",\n                                        children: currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                showAddToCart && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"primary\",\n                                    size: \"sm\",\n                                    className: \"flex-1 rounded-md\",\n                                    onClick: handleAddToCart,\n                                    disabled: !isInStock,\n                                    \"aria-label\": t('shop.addToCart'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                showQuickView && onQuickView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"rounded-md\",\n                                    onClick: handleQuickView,\n                                    \"aria-label\": t('shop.quickView'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\product\\\\ProductCard.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"LGLZpon4bxDk8oT9xNGcBFYt2Ug=\", false, function() {\n    return [\n        _translations__WEBPACK_IMPORTED_MODULE_10__.useTranslation,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore,\n        _stores_cartStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore,\n        _stores_wishlistStore__WEBPACK_IMPORTED_MODULE_7__.useWishlistStore\n    ];\n});\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/product/ProductCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/FeaturedProducts.tsx":
/*!******************************************************!*\
  !*** ./src/components/sections/FeaturedProducts.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _product_ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../product/ProductCard */ \"(app-pages-browser)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _services_ProductService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/ProductService */ \"(app-pages-browser)/./src/services/ProductService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst FeaturedProducts = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function FeaturedProducts() {\n    _s();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore)();\n    const isRTL = language === 'ar';\n    // Simple translation function\n    const t = (key, fallback)=>fallback;\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeaturedProducts.FeaturedProducts.useEffect\": ()=>{\n            const loadFeaturedProducts = {\n                \"FeaturedProducts.FeaturedProducts.useEffect.loadFeaturedProducts\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const featuredProducts = await _services_ProductService__WEBPACK_IMPORTED_MODULE_6__.ProductService.getFeaturedProducts(8);\n                        setProducts(featuredProducts);\n                    } catch (error) {\n                        console.error('Failed to load featured products:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FeaturedProducts.FeaturedProducts.useEffect.loadFeaturedProducts\"];\n            loadFeaturedProducts();\n        }\n    }[\"FeaturedProducts.FeaturedProducts.useEffect\"], []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: 'easeOut'\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: Array.from({\n                            length: 8\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-square bg-gray-200 rounded-lg mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-2/3 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('featuredProducts.title', 'Featured Products')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t('featuredProducts.description', 'Discover our handpicked selection of premium products, carefully chosen for their quality and value.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_ProductCard__WEBPACK_IMPORTED_MODULE_4__.ProductCard, {\n                                product: product,\n                                showQuickView: true,\n                                showWishlist: true,\n                                className: \"h-full hover:shadow-lg transition-shadow duration-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, product.id, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        asChild: true,\n                        size: \"lg\",\n                        variant: \"outline\",\n                        className: \"border-2 border-blue-500 text-blue-600 hover:bg-blue-500 hover:text-white px-8 py-3 rounded-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop\",\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                t('featuredProducts.viewAll', 'View All Products'),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 \".concat(isRTL ? 'rotate-180' : '')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: \"100%\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.authentic', 'Authentic Products')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: \"24/7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.support', 'Customer Support')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: \"Free\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.shipping', 'Fast Shipping')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-orange-600\",\n                                    children: \"30\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('trust.returns', 'Day Returns')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedProducts.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}, \"057S10+Gv7osBWLc5DmEOVxJnKU=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n})), \"057S10+Gv7osBWLc5DmEOVxJnKU=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n});\n_c1 = FeaturedProducts;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeaturedProducts);\nvar _c, _c1;\n$RefreshReg$(_c, \"FeaturedProducts$memo\");\n$RefreshReg$(_c1, \"FeaturedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/FeaturedProducts.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/EnhancedImage.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/EnhancedImage.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedImage: () => (/* binding */ EnhancedImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* __next_internal_client_entry_do_not_use__ EnhancedImage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction EnhancedImage(param) {\n    let { src, alt, width, height, className, containerClassName, priority = false, effect = 'none', rounded = 'md', aspectRatio = 'auto', objectFit = 'cover', quality = 85, loading = 'lazy', fill = false, sizes, onClick, progressive = false, placeholder = 'blur', onError } = param;\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_4__.useThemeStore)();\n    // تعيين نسبة العرض إلى الارتفاع\n    const getAspectRatio = ()=>{\n        switch(aspectRatio){\n            case '1:1':\n                return 'aspect-square';\n            case '4:3':\n                return 'aspect-[4/3]';\n            case '16:9':\n                return 'aspect-[16/9]';\n            case '21:9':\n                return 'aspect-[21/9]';\n            default:\n                return '';\n        }\n    };\n    // تعيين حجم التقريب\n    const getRoundedSize = ()=>{\n        switch(rounded){\n            case 'none':\n                return '';\n            case 'sm':\n                return 'rounded-sm';\n            case 'md':\n                return 'rounded-md';\n            case 'lg':\n                return 'rounded-lg';\n            case 'full':\n                return 'rounded-full';\n            default:\n                return 'rounded-md';\n        }\n    };\n    // تعيين نوع التأثير\n    const getEffectStyles = ()=>{\n        switch(effect){\n            case 'zoom':\n                return 'group-hover:scale-110 transition-transform duration-500';\n            case 'fade':\n                return 'opacity-90 group-hover:opacity-100 transition-opacity duration-300';\n            case 'blur':\n                return 'blur-[2px] group-hover:blur-0 transition-all duration-300';\n            case 'tilt':\n                return '';\n            case 'shine':\n                return 'relative';\n            default:\n                return '';\n        }\n    };\n    // تعيين نوع الاحتواء\n    const getObjectFit = ()=>{\n        switch(objectFit){\n            case 'cover':\n                return 'object-cover';\n            case 'contain':\n                return 'object-contain';\n            case 'fill':\n                return 'object-fill';\n            case 'none':\n                return 'object-none';\n            default:\n                return 'object-cover';\n        }\n    };\n    // تأثير العنصر النائب\n    const renderPlaceholder = ()=>{\n        if (!isLoaded) {\n            switch(placeholder){\n                case 'blur':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this);\n                case 'shimmer':\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, this);\n                default:\n                    return null;\n            }\n        }\n        return null;\n    };\n    // تحسين تجربة المستخدم عند تحميل الصور\n    const handleImageLoad = ()=>{\n        setIsLoaded(true);\n        // تأخير إظهار الصورة قليلاً لتجنب الوميض\n        if (progressive) {\n            setTimeout(()=>{\n                const imageElement = document.getElementById(\"enhanced-image-\".concat(src.split('/').pop()));\n                if (imageElement) {\n                    imageElement.classList.remove('opacity-0');\n                    imageElement.classList.add('opacity-100');\n                }\n            }, 50);\n        }\n    };\n    // معالجة خطأ تحميل الصورة\n    const handleImageError = ()=>{\n        setHasError(true);\n        if (onError) onError();\n        console.warn(\"Failed to load image: \".concat(src));\n    };\n    // استخدام صورة احتياطية في حالة الخطأ\n    const fallbackSrc = hasError ? \"/images/placeholder-\".concat(isDarkMode ? 'dark' : 'light', \".svg\") : src;\n    // تحميل الصورة مسبقًا للتحقق من صحتها\n    const preloadImage = (url)=>{\n        if ( true && !hasError) {\n            const img = document.createElement('img');\n            img.src = url;\n            img.onerror = handleImageError;\n        }\n    };\n    // تحميل الصورة مسبقًا إذا كانت ذات أولوية\n    if (priority && \"object\" !== 'undefined') {\n        preloadImage(src);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative overflow-hidden group', getAspectRatio(), getRoundedSize(), isDarkMode ? 'bg-slate-800' : 'bg-slate-100', onClick && 'cursor-pointer', containerClassName),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"enhanced-image-\".concat(src.split('/').pop()),\n                src: fallbackSrc,\n                alt: alt,\n                width: width,\n                height: height,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(getObjectFit(), getEffectStyles(), 'transition-all duration-300', !isLoaded && progressive ? 'opacity-0' : 'opacity-100', className),\n                priority: priority,\n                quality: quality,\n                loading: priority ? 'eager' : loading,\n                fill: fill,\n                sizes: sizes || (fill ? '100vw' : undefined),\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                unoptimized: hasError\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            renderPlaceholder()\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\EnhancedImage.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(EnhancedImage, \"0uqAHPDKRYjjtOOHHxG9m86E0Cs=\", false, function() {\n    return [\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_4__.useThemeStore\n    ];\n});\n_c = EnhancedImage;\nvar _c;\n$RefreshReg$(_c, \"EnhancedImage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/EnhancedImage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/animations/HoverAnimation.tsx":
/*!*********************************************************!*\
  !*** ./src/components/ui/animations/HoverAnimation.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverAnimation: () => (/* binding */ HoverAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ HoverAnimation auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HoverAnimation(param) {\n    let { children, className, animation = 'scale', scale = 1.05, duration = 0.3, disabled = false, as = 'div', onClick, ...props } = param;\n    _s();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HoverAnimation.useEffect\": ()=>{\n            setIsMounted(true);\n            setIsMobile(window.innerWidth < 768);\n            const handleResize = {\n                \"HoverAnimation.useEffect.handleResize\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"HoverAnimation.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"HoverAnimation.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"HoverAnimation.useEffect\"];\n        }\n    }[\"HoverAnimation.useEffect\"], []);\n    // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true\n    const isAnimationDisabled = disabled || isMobile && typeof document !== 'undefined' && document.documentElement.classList.contains('mobile-device');\n    // تحسين الأداء عن طريق استخدام will-change للعناصر التي تحتاج إلى تحسين أداء الرسوم المتحركة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HoverAnimation.useEffect\": ()=>{\n            if (isMounted && !isAnimationDisabled) {\n                const needsHardwareAcceleration = [\n                    'lift',\n                    'rotate',\n                    'tilt',\n                    'pulse',\n                    'bounce'\n                ].includes(animation);\n                if (needsHardwareAcceleration) {\n                    const element = document.querySelector(\".animation-\".concat(animation));\n                    if (element) {\n                        element.classList.add('will-change-transform');\n                    }\n                }\n            }\n        }\n    }[\"HoverAnimation.useEffect\"], [\n        isMounted,\n        isAnimationDisabled,\n        animation\n    ]);\n    // تحديد متغيرات الرسوم المتحركة بناءً على النوع\n    const getVariants = ()=>{\n        const baseTransition = {\n            type: 'tween',\n            duration\n        };\n        switch(animation){\n            case 'scale':\n                return {\n                    initial: {},\n                    hover: {\n                        scale,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        scale: scale * 0.95,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'lift':\n                return {\n                    initial: {},\n                    hover: {\n                        y: -8,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        y: -4,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'glow':\n                return {\n                    initial: {},\n                    hover: {\n                        boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n                        transition: baseTransition\n                    },\n                    tap: {\n                        boxShadow: '0 0 8px rgba(var(--color-primary-500), 0.3)',\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'rotate':\n                return {\n                    initial: {},\n                    hover: {\n                        rotate: 5,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        rotate: 2,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'pulse':\n                return {\n                    initial: {},\n                    hover: {\n                        scale: [\n                            1,\n                            scale,\n                            1,\n                            scale,\n                            1\n                        ],\n                        transition: {\n                            duration: duration * 2,\n                            repeat: Infinity,\n                            repeatType: 'loop'\n                        }\n                    },\n                    tap: {\n                        scale: scale * 0.95,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'bounce':\n                return {\n                    initial: {},\n                    hover: {\n                        y: [\n                            0,\n                            -10,\n                            0\n                        ],\n                        transition: {\n                            duration: duration * 1.5,\n                            repeat: Infinity,\n                            repeatType: 'loop'\n                        }\n                    },\n                    tap: {\n                        y: -5,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'tilt':\n                return {\n                    initial: {},\n                    hover: {\n                        rotateX: -10,\n                        rotateY: 10,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        rotateX: -5,\n                        rotateY: 5,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'shine':\n                return {\n                    initial: {},\n                    hover: {},\n                    tap: {\n                        scale: 0.98,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'border':\n                return {\n                    initial: {\n                        borderColor: 'rgba(var(--color-primary-500), 0)'\n                    },\n                    hover: {\n                        borderColor: 'rgba(var(--color-primary-500), 1)',\n                        borderWidth: '2px',\n                        transition: baseTransition\n                    },\n                    tap: {\n                        borderColor: 'rgba(var(--color-primary-600), 1)',\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'shadow':\n                return {\n                    initial: {\n                        boxShadow: '0 0 0 rgba(0, 0, 0, 0)'\n                    },\n                    hover: {\n                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',\n                        y: -2,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        boxShadow: '0 5px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',\n                        y: -1,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            case 'fade':\n                return {\n                    initial: {\n                        opacity: 1\n                    },\n                    hover: {\n                        opacity: 0.8,\n                        transition: baseTransition\n                    },\n                    tap: {\n                        opacity: 0.9,\n                        transition: {\n                            ...baseTransition,\n                            duration: duration / 2\n                        }\n                    }\n                };\n            default:\n                return {\n                    initial: {},\n                    hover: {},\n                    tap: {}\n                };\n        }\n    };\n    const variants = getVariants();\n    const Component = framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion[as] || framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div;\n    // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة\n    if (isAnimationDisabled || !isMounted) {\n        const ElementType = as;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ElementType, {\n            className: className,\n            onClick: onClick,\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(className, \"animation-\".concat(animation), animation === 'shine' && 'group overflow-hidden relative', animation === 'border' && 'border border-transparent', animation === 'shadow' && 'transition-shadow'),\n        initial: \"initial\",\n        whileHover: \"hover\",\n        whileTap: \"tap\",\n        variants: variants,\n        onClick: onClick,\n        ...props,\n        children: [\n            children,\n            animation === 'shine' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:translate-x-full\",\n                transition: {\n                    duration: duration * 2,\n                    ease: 'linear'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this),\n            animation === 'glow' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100\",\n                initial: {\n                    opacity: 0,\n                    boxShadow: '0 0 0 rgba(var(--color-primary-500), 0)'\n                },\n                whileHover: {\n                    opacity: 1,\n                    boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n                    transition: {\n                        duration\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\ui\\\\animations\\\\HoverAnimation.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_s(HoverAnimation, \"j+LgGGV/G96wZxwF/2a9DWGCPuM=\");\n_c = HoverAnimation;\nvar _c;\n$RefreshReg$(_c, \"HoverAnimation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/animations/HoverAnimation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/ProductService.ts":
/*!****************************************!*\
  !*** ./src/services/ProductService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductService: () => (/* binding */ ProductService),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   filterProductsByCategory: () => (/* binding */ filterProductsByCategory),\n/* harmony export */   getAllProducts: () => (/* binding */ getAllProducts),\n/* harmony export */   getFeaturedProducts: () => (/* binding */ getFeaturedProducts),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductBySlug: () => (/* binding */ getProductBySlug),\n/* harmony export */   initializeDefaultProducts: () => (/* binding */ initializeDefaultProducts),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_sqlite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/sqlite */ \"(app-pages-browser)/./src/lib/sqlite.ts\");\n/* harmony import */ var _data_products__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/products */ \"(app-pages-browser)/./src/data/products.ts\");\n/**\n * خدمة إدارة المنتجات باستخدام SQLite\n */ \n\n// Initialize the db instance\nconst sqliteDB = (0,_lib_sqlite__WEBPACK_IMPORTED_MODULE_0__.getDbInstance)();\n// مفتاح التخزين المحلي\nconst LOCAL_PRODUCTS_KEY = 'local-products';\n/**\n * الحصول على جميع المنتجات\n */ async function getAllProducts() {\n    try {\n        // محاولة الحصول على المنتجات من SQLite\n        const products = await sqliteDB.getProducts();\n        // إذا لم تكن هناك منتجات، قم بتهيئة المنتجات الافتراضية\n        if ((await sqliteDB.getProducts()).length === 0) {\n            await initializeDefaultProducts();\n            return await sqliteDB.getProducts();\n        }\n        return products;\n    } catch (error) {\n        console.error('Error getting products:', error);\n        return [];\n    }\n}\n/**\n * الحصول على منتج بواسطة المعرف\n */ async function getProductById(id) {\n    try {\n        const products = await sqliteDB.getProducts();\n        return products.find((p)=>p.id.toString() === id.toString()) || null;\n    } catch (error) {\n        console.error(\"Error getting product by ID \".concat(id, \":\"), error);\n        return null;\n    }\n}\n/**\n * الحصول على منتج بواسطة الرابط\n */ async function getProductBySlug(slug) {\n    try {\n        const products = await sqliteDB.getProducts();\n        return products.find((p)=>p.slug === slug) || null;\n    } catch (error) {\n        console.error(\"Error getting product by slug \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n/**\n * إنشاء منتج جديد\n */ async function createProduct(productData) {\n    try {\n        const products = await sqliteDB.getProducts();\n        // إنشاء معرف فريد للمنتج الجديد\n        const id = productData.id || \"product-\".concat(Date.now().toString());\n        // دمج بيانات المنتج مع القيم الافتراضية\n        const newProduct = {\n            id: id,\n            name: productData.name || \"منتج جديد\",\n            slug: productData.slug || \"new-product-\".concat(Date.now().toString()),\n            description: productData.description || \"وصف المنتج الجديد\",\n            price: productData.price || 0,\n            compareAtPrice: productData.compareAtPrice || undefined,\n            category: productData.category || \"غير مصنف\",\n            images: productData.images || [],\n            tags: productData.tags || [],\n            stock: productData.stock || 0,\n            featured: productData.featured || false,\n            specifications: productData.specifications || {},\n            reviews: productData.reviews || [],\n            rating: productData.rating || 0,\n            reviewCount: productData.reviewCount || 0,\n            relatedProducts: productData.relatedProducts || [],\n            createdAt: new Date().toISOString()\n        };\n        // إضافة المنتج الجديد إلى المنتجات - relies on createProduct to persist\n        const createdProduct = await sqliteDB.createProduct(newProduct);\n        if (!createdProduct) {\n            throw new Error('Failed to create product in DB');\n        }\n        return createdProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('فشل إنشاء المنتج');\n    }\n}\n/**\n * تحديث منتج\n */ async function updateProduct(id, productData) {\n    try {\n        const products = await sqliteDB.getProducts();\n        const index = products.findIndex((p)=>p.id.toString() === id.toString());\n        if (index === -1) {\n            return null;\n        }\n        const updatedProductData = {\n            ...products[index],\n            ...productData\n        };\n        // تحديث المنتج في المنتجات - relies on updateProduct to persist\n        const updatedProduct = await sqliteDB.updateProduct(id.toString(), updatedProductData);\n        if (!updatedProduct) {\n            return null;\n        }\n        return updatedProduct;\n    } catch (error) {\n        console.error(\"Error updating product \".concat(id, \":\"), error);\n        return null;\n    }\n}\n/**\n * حذف منتج\n */ async function deleteProduct(id) {\n    try {\n        const products = await sqliteDB.getProducts();\n        const productExists = products.some((p)=>p.id.toString() === id.toString());\n        if (!productExists) {\n            return false; // Product not found, cannot delete\n        }\n        // حذف المنتج من المنتجات - relies on deleteProduct to persist\n        const success = await sqliteDB.deleteProduct(id.toString());\n        return success;\n    } catch (error) {\n        console.error(\"Error deleting product \".concat(id, \":\"), error);\n        return false;\n    }\n}\n/**\n * البحث عن منتجات\n */ async function searchProducts(query) {\n    try {\n        const products = await sqliteDB.getProducts();\n        if (!query) {\n            return products;\n        }\n        const lowerCaseQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerCaseQuery) || product.description.toLowerCase().includes(lowerCaseQuery) || product.tags && product.tags.some((tag)=>tag.toLowerCase().includes(lowerCaseQuery)));\n    } catch (error) {\n        console.error('Error searching products:', error);\n        return [];\n    }\n}\n/**\n * تصفية المنتجات حسب الفئة\n */ async function filterProductsByCategory(category) {\n    try {\n        const products = await sqliteDB.getProducts();\n        if (!category) {\n            return products;\n        }\n        return products.filter((p)=>p.category === category);\n    } catch (error) {\n        console.error(\"Error filtering products by category \".concat(category, \":\"), error);\n        return [];\n    }\n}\n/**\n * الحصول على المنتجات المميزة\n */ async function getFeaturedProducts() {\n    try {\n        const products = await sqliteDB.getProducts();\n        return products.filter((p)=>p.featured);\n    } catch (error) {\n        console.error('Error getting featured products:', error);\n        return [];\n    }\n}\n/**\n * تهيئة المنتجات الافتراضية\n */ async function initializeDefaultProducts() {\n    try {\n        const currentProducts = await sqliteDB.getProducts();\n        if (currentProducts.length === 0) {\n            console.log(\"[ProductService] Initializing default products as DB is empty.\");\n            for (const product of _data_products__WEBPACK_IMPORTED_MODULE_1__.products){\n                await sqliteDB.createProduct(product); // createProduct handles saving\n            }\n        }\n    } catch (error) {\n        console.error('Error initializing default products:', error);\n    }\n}\n// تهيئة المنتجات الافتراضية عند تحميل الخدمة\nif (true) {\n    initializeDefaultProducts();\n}\n// Export as a class for easier usage\nclass ProductService {\n    static async getAllProducts() {\n        return getAllProducts();\n    }\n    static async getProductById(id) {\n        return getProductById(id);\n    }\n    static async getProductBySlug(slug) {\n        return getProductBySlug(slug);\n    }\n    static async createProduct(productData) {\n        return createProduct(productData);\n    }\n    static async updateProduct(id, productData) {\n        return updateProduct(id, productData);\n    }\n    static async deleteProduct(id) {\n        return deleteProduct(id);\n    }\n    static async searchProducts(query) {\n        return searchProducts(query);\n    }\n    static async filterProductsByCategory(category) {\n        return filterProductsByCategory(category);\n    }\n    static async getFeaturedProducts(limit) {\n        const products = await getFeaturedProducts();\n        return limit ? products.slice(0, limit) : products;\n    }\n    static async initializeDefaultProducts() {\n        return initializeDefaultProducts();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/ProductService.ts\n"));

/***/ })

}]);