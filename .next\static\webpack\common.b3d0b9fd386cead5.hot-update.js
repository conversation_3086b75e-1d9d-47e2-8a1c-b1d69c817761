"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("common",{

/***/ "(app-pages-browser)/./src/stores/languageStore.ts":
/*!*************************************!*\
  !*** ./src/stores/languageStore.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLanguageStore: () => (/* binding */ useLanguageStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Simple translation dictionary\nconst translations = {\n    en: {\n        'hero.badge': 'Enterprise E-commerce Platform',\n        'hero.title': 'Build Your',\n        'hero.titleHighlight': 'Dream Store',\n        'hero.titleEnd': 'Today',\n        'hero.description': 'Professional e-commerce platform with advanced security, payment integration, and comprehensive admin dashboard. Built with modern technologies for optimal performance.',\n        'hero.shopNow': 'Shop Now',\n        'hero.watchDemo': 'Watch Demo',\n        'categories.title': 'Shop by Category',\n        'categories.description': 'Explore our wide range of product categories and find exactly what you\\'re looking for.',\n        'categories.shopNow': 'Shop Now',\n        'categories.browseAll': 'Browse All Products',\n        'featuredProducts.title': 'Featured Products',\n        'featuredProducts.description': 'Discover our handpicked selection of premium products, carefully chosen for their quality and value.',\n        'featuredProducts.viewAll': 'View All Products',\n        'services.title': 'Why Choose Us',\n        'services.description': 'We provide exceptional service and support to ensure your shopping experience is seamless and enjoyable.',\n        'services.cta.title': 'Need Help?',\n        'services.cta.description': 'Our customer service team is here to help you with any questions or concerns you may have.',\n        'services.cta.button': 'Contact Support',\n        'testimonials.title': 'What Our Customers Say',\n        'testimonials.description': 'Don\\'t just take our word for it. Here\\'s what our satisfied customers have to say about their experience.',\n        'newsletter.title': 'Stay Updated',\n        'newsletter.description': 'Subscribe to our newsletter and be the first to know about new products, special offers, and exclusive deals.',\n        'newsletter.subscribe': 'Subscribe',\n        'newsletter.subscribing': 'Subscribing...',\n        'newsletter.emailPlaceholder': 'Enter your email address',\n        'newsletter.privacy': 'We respect your privacy. Unsubscribe at any time.'\n    },\n    ar: {\n        'hero.badge': 'منصة تجارة إلكترونية متقدمة',\n        'hero.title': 'ابني',\n        'hero.titleHighlight': 'متجرك المثالي',\n        'hero.titleEnd': 'اليوم',\n        'hero.description': 'منصة تجارة إلكترونية احترافية مع أمان متقدم وتكامل المدفوعات ولوحة تحكم شاملة. مبنية بتقنيات حديثة للأداء الأمثل.',\n        'hero.shopNow': 'تسوق الآن',\n        'hero.watchDemo': 'شاهد العرض',\n        'categories.title': 'تسوق حسب الفئة',\n        'categories.description': 'استكشف مجموعتنا الواسعة من فئات المنتجات واعثر على ما تبحث عنه بالضبط.',\n        'categories.shopNow': 'تسوق الآن',\n        'categories.browseAll': 'تصفح جميع المنتجات',\n        'featuredProducts.title': 'المنتجات المميزة',\n        'featuredProducts.description': 'اكتشف مجموعتنا المختارة بعناية من المنتجات المميزة، المختارة بعناية لجودتها وقيمتها.',\n        'featuredProducts.viewAll': 'عرض جميع المنتجات',\n        'services.title': 'لماذا تختارنا',\n        'services.description': 'نحن نقدم خدمة ودعم استثنائيين لضمان أن تكون تجربة التسوق الخاصة بك سلسة وممتعة.',\n        'services.cta.title': 'تحتاج مساعدة؟',\n        'services.cta.description': 'فريق خدمة العملاء لدينا هنا لمساعدتك في أي أسئلة أو مخاوف قد تكون لديك.',\n        'services.cta.button': 'اتصل بالدعم',\n        'testimonials.title': 'ماذا يقول عملاؤنا',\n        'testimonials.description': 'لا تأخذ كلامنا فقط. إليك ما يقوله عملاؤنا الراضون عن تجربتهم.',\n        'newsletter.title': 'ابق على اطلاع',\n        'newsletter.description': 'اشترك في نشرتنا الإخبارية وكن أول من يعرف عن المنتجات الجديدة والعروض الخاصة والصفقات الحصرية.',\n        'newsletter.subscribe': 'اشترك',\n        'newsletter.subscribing': 'جاري الاشتراك...',\n        'newsletter.emailPlaceholder': 'أدخل عنوان بريدك الإلكتروني',\n        'newsletter.privacy': 'نحن نحترم خصوصيتك. إلغاء الاشتراك في أي وقت.'\n    }\n};\nconst useLanguageStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        language: 'en',\n        direction: 'ltr',\n        initialize: ()=>{\n            console.log('Language store: initializing');\n        // Language is already persisted, no additional initialization needed\n        },\n        setLanguage: (language)=>set({\n                language,\n                direction: language === 'ar' ? 'rtl' : 'ltr'\n            }),\n        t: (key, fallback)=>{\n            var _translations_language;\n            const { language } = get();\n            const translation = (_translations_language = translations[language]) === null || _translations_language === void 0 ? void 0 : _translations_language[key];\n            return translation || fallback || key;\n        }\n    }), {\n    name: 'language-storage'\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/languageStore.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/translations/index.ts":
/*!***********************************!*\
  !*** ./src/translations/index.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLocale: () => (/* binding */ useLocale),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n\nconst translations = {\n    en: {\n        // App Info\n        'app.name': 'ARTAL',\n        'app.tagline': 'Your Complete Business Solution',\n        // Navigation\n        'nav.home': 'Home',\n        'nav.shop': 'Shop',\n        'nav.productionLines': 'Production Lines',\n        'nav.services': 'Services',\n        'nav.blog': 'Blog',\n        'nav.contact': 'Contact',\n        // Header Actions\n        'header.search': 'Search',\n        'header.searchPlaceholder': 'Search products...',\n        'header.specialOffers': 'Special Offers',\n        'header.wishlist': 'Wishlist',\n        'header.cart': 'Cart',\n        'header.account': 'Account',\n        'header.signIn': 'Sign In',\n        'header.signUp': 'Sign Up',\n        // Hero Section\n        'hero.cta.explore': 'Explore Products',\n        'hero.cta.services': 'Our Services',\n        // Business Solutions\n        'solutions.title': 'Comprehensive Business Solutions',\n        'solutions.subtitle': 'Explore our full range of services designed to meet all your commercial needs.',\n        'solutions.features': 'Key Features',\n        // Products Section\n        'products.title': 'Featured Products',\n        'products.subtitle': 'Discover our latest innovations and best-selling industrial solutions.',\n        'products.viewAll': 'View All Products',\n        'products.newArrivals': 'New Arrivals',\n        'products.inStock': 'In Stock',\n        'products.outOfStock': 'Out of Stock',\n        'products.addToCart': 'Add to Cart',\n        'products.requestQuote': 'Request Quote',\n        // Blog Section\n        'blog.title': 'Latest Industry Insights',\n        'blog.subtitle': 'Stay informed with our latest articles, trends, and industry news.',\n        'blog.readMore': 'Read More',\n        'blog.viewAll': 'View All Articles',\n        // Common Actions\n        'actions.viewDetails': 'View Details',\n        'actions.learnMore': 'Learn More',\n        'actions.getStarted': 'Get Started',\n        'actions.contactUs': 'Contact Us',\n        'actions.bookNow': 'Book Now',\n        // Authentication\n        'auth.signIn': 'Sign In',\n        'auth.signUp': 'Sign Up',\n        'auth.email': 'Email',\n        'auth.password': 'Password',\n        'auth.forgotPassword': 'Forgot Password?',\n        'auth.firstName': 'First Name',\n        'auth.lastName': 'Last Name',\n        'auth.processing': 'Processing...',\n        'auth.noAccount': 'Don\\'t have an account?',\n        'auth.haveAccount': 'Already have an account?',\n        'auth.signInSuccess': 'Signed in successfully!',\n        'auth.signUpSuccess': 'Account created successfully!',\n        'auth.genericError': 'An error occurred. Please try again.',\n        'auth.emailRequired': 'Email is required',\n        'auth.invalidEmail': 'Invalid email format',\n        'auth.passwordRequired': 'Password is required',\n        'auth.passwordTooShort': 'Password must be at least 6 characters',\n        'auth.firstNameRequired': 'First name is required',\n        'auth.lastNameRequired': 'Last name is required',\n        'auth.passwordRequirements': 'Password must be at least 6 characters',\n        'auth.invalidCredentials': 'Invalid email or password',\n        'auth.emailAlreadyInUse': 'This email is already registered',\n        'auth.accountCreated': 'Account created successfully',\n        'auth.loginSuccess': 'Logged in successfully',\n        // Cart\n        'cart.title': 'Your Cart',\n        'cart.empty': 'Your cart is empty',\n        'cart.emptyMessage': 'Add some items to your cart to get started',\n        'cart.continueShopping': 'Continue Shopping',\n        'cart.subtotal': 'Subtotal',\n        'cart.shipping': 'Shipping',\n        'cart.calculatedAtCheckout': 'Calculated at checkout',\n        'cart.tax': 'Tax',\n        'cart.total': 'Total',\n        'cart.proceedToCheckout': 'Proceed to Checkout',\n        'cart.remove': 'Remove',\n        'cart.quantity': 'Quantity',\n        'cart.clearCart': 'Clear Cart',\n        'cart.orderSummary': 'Order Summary',\n        // Wishlist\n        'wishlist.title': 'My Wishlist',\n        'wishlist.empty': 'Your wishlist is empty',\n        'wishlist.emptyMessage': 'Add items to your wishlist to save them for later',\n        'wishlist.continueShopping': 'Continue Shopping',\n        'wishlist.clearAll': 'Clear All',\n        'wishlist.addAllToCart': 'Add All to Cart',\n        'wishlist.remove': 'Remove',\n        'wishlist.addToCart': 'Add to Cart',\n        'wishlist.alreadyInCart': 'Already in cart',\n        // Account\n        'account.myAccount': 'My Account',\n        'account.profile': 'Profile',\n        'account.orders': 'Orders',\n        'account.addresses': 'Addresses',\n        'account.paymentMethods': 'Payment Methods',\n        'account.wishlist': 'Wishlist',\n        'account.loyalty': 'Loyalty Program',\n        'account.settings': 'Settings',\n        'account.signOut': 'Sign Out',\n        'account.notLoggedIn': 'Not logged in',\n        'account.loginRequired': 'You need to log in to access your account',\n        'account.profileInformation': 'Profile Information',\n        'account.firstName': 'First Name',\n        'account.lastName': 'Last Name',\n        'account.email': 'Email',\n        'account.phone': 'Phone',\n        'account.company': 'Company',\n        'account.emailCannotBeChanged': 'Email address cannot be changed',\n        'account.saveChanges': 'Save Changes',\n        'account.profileUpdated': 'Profile updated successfully',\n        'account.myOrders': 'My Orders',\n        'account.shippingAddresses': 'Shipping Addresses',\n        // Orders\n        'orders.searchOrders': 'Search orders',\n        'orders.allOrders': 'All Orders',\n        'orders.statusProcessing': 'Processing',\n        'orders.statusShipped': 'Shipped',\n        'orders.statusDelivered': 'Delivered',\n        'orders.statusCancelled': 'Cancelled',\n        'orders.noOrders': 'No orders found',\n        'orders.noOrdersMatchingFilters': 'No orders matching your filters',\n        'orders.noOrdersYet': 'You haven\\'t placed any orders yet',\n        'orders.items': 'items',\n        'orders.orderItems': 'Order Items',\n        'orders.quantity': 'Quantity',\n        'orders.shippingAddress': 'Shipping Address',\n        'orders.tracking': 'Tracking Information',\n        'orders.trackPackage': 'Track Package',\n        // Addresses\n        'addresses.addNew': 'Add New Address',\n        'addresses.editAddress': 'Edit Address',\n        'addresses.addNewAddress': 'Add New Address',\n        'addresses.fullName': 'Full Name',\n        'addresses.streetAddress': 'Street Address',\n        'addresses.city': 'City',\n        'addresses.stateProvince': 'State/Province',\n        'addresses.postalCode': 'Postal Code',\n        'addresses.country': 'Country',\n        'addresses.setAsDefault': 'Set as default address',\n        'addresses.addAddress': 'Add Address',\n        'addresses.noAddresses': 'No addresses found',\n        'addresses.addAddressPrompt': 'Add a shipping address to speed up checkout',\n        'addresses.addFirst': 'Add Your First Address',\n        'addresses.default': 'Default',\n        'addresses.setDefault': 'Set as Default',\n        // Payment Methods\n        'payment.addNew': 'Add New Payment Method',\n        'payment.editCard': 'Edit Card',\n        'payment.addNewCard': 'Add New Card',\n        'payment.cardNumber': 'Card Number',\n        'payment.cardholderName': 'Cardholder Name',\n        'payment.expiryDate': 'Expiry Date',\n        'payment.cvv': 'CVV',\n        'payment.setAsDefault': 'Set as default payment method',\n        'payment.addCard': 'Add Card',\n        'payment.noCards': 'No payment methods found',\n        'payment.addCardPrompt': 'Add a payment method to speed up checkout',\n        'payment.addFirst': 'Add Your First Payment Method',\n        'payment.default': 'Default',\n        'payment.setDefault': 'Set as Default',\n        'payment.expires': 'Expires',\n        'payment.securityNote': 'For demonstration purposes only. Do not enter real card information.',\n        'payment.creditCard': 'Credit Card',\n        'payment.paypal': 'PayPal',\n        'payment.bankTransfer': 'Bank Transfer',\n        // Settings\n        'settings.password': 'Password',\n        'settings.notifications': 'Notifications',\n        'settings.preferences': 'Preferences',\n        'settings.currentPassword': 'Current Password',\n        'settings.newPassword': 'New Password',\n        'settings.confirmPassword': 'Confirm Password',\n        'settings.changePassword': 'Change Password',\n        'settings.passwordChanged': 'Password changed successfully',\n        'settings.emailNotifications': 'Enable email notifications',\n        'settings.orderUpdates': 'Order status updates',\n        'settings.promotions': 'Promotions and special offers',\n        'settings.newsletter': 'Newsletter subscription',\n        'settings.notificationsUpdated': 'Notification preferences updated',\n        'settings.savePreferences': 'Save Preferences',\n        'settings.language': 'Language',\n        'settings.currency': 'Currency',\n        'settings.theme': 'Theme',\n        'settings.lightMode': 'Light Mode',\n        'settings.darkMode': 'Dark Mode',\n        // Common\n        'common.cancel': 'Cancel',\n        'common.save': 'Save',\n        'common.edit': 'Edit',\n        'common.delete': 'Delete',\n        // Wholesale Quote Form\n        'wholesale.wholesaleTitle': 'Wholesale Quote Request',\n        'wholesale.customProductTitle': 'Custom Product Quote Request',\n        'wholesale.companyName': 'Company Name',\n        'wholesale.contactName': 'Contact Name',\n        'wholesale.email': 'Email',\n        'wholesale.phone': 'Phone',\n        'wholesale.productType': 'Product Type',\n        'wholesale.productTypePlaceholder': 'e.g., Electronics, Machinery, Raw Materials',\n        'wholesale.specifications': 'Product Specifications',\n        'wholesale.specificationsPlaceholder': 'Please provide detailed specifications for your product requirements',\n        'wholesale.targetQuantity': 'Target Quantity',\n        'wholesale.targetQuantityPlaceholder': 'e.g., 1000 units',\n        'wholesale.targetPrice': 'Target Price (Optional)',\n        'wholesale.targetPricePlaceholder': 'Your desired price point',\n        'wholesale.timeline': 'Timeline',\n        'wholesale.timelinePlaceholder': 'When do you need the products?',\n        'wholesale.additionalNotes': 'Additional Notes',\n        'wholesale.additionalNotesPlaceholder': 'Any other information you\\'d like to share',\n        'wholesale.uploadFiles': 'Upload Files (Optional)',\n        'wholesale.dropFilesHere': 'Drop files here or click to upload product drawings, specifications, or reference images',\n        'wholesale.selectFiles': 'Select Files',\n        'wholesale.cancel': 'Cancel',\n        'wholesale.submitRequest': 'Submit Request',\n        'wholesale.submitting': 'Submitting...',\n        'wholesale.requestSubmitted': 'Request Submitted',\n        'wholesale.thankYou': 'Thank you for your request. Our team will contact you shortly.',\n        'wholesale.close': 'Close',\n        'wholesale.authRequired': 'You must be logged in to submit a quote request',\n        'wholesale.submitError': 'An error occurred while submitting your request. Please try again.',\n        // Common (Added placeholders)\n        'common.yes': 'Yes',\n        'common.no': 'No',\n        'common.confirm': 'Confirm',\n        'common.close': 'Close',\n        'common.loading': 'Loading',\n        'common.error': 'Error',\n        'common.success': 'Success',\n        'common.previous': 'Previous',\n        'common.next': 'Next',\n        // Footer\n        'footer.quickLinks': 'Quick Links',\n        'footer.support': 'Customer Support',\n        'footer.contact': 'Contact Information',\n        'footer.rights': 'All rights reserved'\n    },\n    ar: {\n        // App Info\n        'app.name': 'ارتال',\n        'app.tagline': 'حلول الأعمال المتكاملة',\n        // Navigation\n        'nav.home': 'الرئيسية',\n        'nav.shop': 'المتجر',\n        'nav.productionLines': 'خطوط الإنتاج',\n        'nav.services': 'الخدمات',\n        'nav.blog': 'المدونة',\n        'nav.contact': 'اتصل بنا',\n        // Header Actions\n        'header.search': 'بحث',\n        'header.searchPlaceholder': 'ابحث عن المنتجات...',\n        'header.specialOffers': 'عروض خاصة',\n        'header.wishlist': 'المفضلة',\n        'header.cart': 'السلة',\n        'header.account': 'الحساب',\n        'header.signIn': 'تسجيل الدخول',\n        'header.signUp': 'إنشاء حساب',\n        // Hero Section\n        'hero.cta.explore': 'استكشف المنتجات',\n        'hero.cta.services': 'خدماتنا',\n        // Business Solutions\n        'solutions.title': 'حلول أعمال شاملة',\n        'solutions.subtitle': 'استكشف مجموعتنا الكاملة من الخدمات المصممة لتلبية جميع احتياجات عملك.',\n        'solutions.features': 'الميزات الرئيسية',\n        // Products Section\n        'products.title': 'المنتجات المميزة',\n        'products.subtitle': 'اكتشف أحدث ابتكاراتنا وأفضل الحلول الصناعية مبيعاً.',\n        'products.viewAll': 'عرض جميع المنتجات',\n        'products.newArrivals': 'وصل حديثاً',\n        'products.inStock': 'متوفر',\n        'products.outOfStock': 'نفذ المخزون',\n        'products.addToCart': 'أضف إلى السلة',\n        'products.requestQuote': 'طلب عرض سعر',\n        // Blog Section\n        'blog.title': 'آخر رؤى الصناعة',\n        'blog.subtitle': 'ابق على اطلاع بأحدث المقالات والاتجاهات وأخبار الصناعة.',\n        'blog.readMore': 'اقرأ المزيد',\n        'blog.viewAll': 'عرض جميع المقالات',\n        // Common Actions\n        'actions.viewDetails': 'عرض التفاصيل',\n        'actions.learnMore': 'اعرف المزيد',\n        'actions.getStarted': 'ابدأ الآن',\n        'actions.contactUs': 'اتصل بنا',\n        'actions.bookNow': 'احجز الآن',\n        // Authentication\n        'auth.signIn': 'تسجيل الدخول',\n        'auth.signUp': 'إنشاء حساب',\n        'auth.email': 'البريد الإلكتروني',\n        'auth.password': 'كلمة المرور',\n        'auth.forgotPassword': 'نسيت كلمة المرور؟',\n        'auth.firstName': 'الاسم الأول',\n        'auth.lastName': 'الاسم الأخير',\n        'auth.processing': 'جاري المعالجة...',\n        'auth.noAccount': 'ليس لديك حساب؟',\n        'auth.haveAccount': 'لديك حساب بالفعل؟',\n        'auth.signInSuccess': 'تم تسجيل الدخول بنجاح!',\n        'auth.signUpSuccess': 'تم إنشاء الحساب بنجاح!',\n        'auth.genericError': 'حدث خطأ. يرجى المحاولة مرة أخرى.',\n        'auth.emailRequired': 'البريد الإلكتروني مطلوب',\n        'auth.invalidEmail': 'تنسيق البريد الإلكتروني غير صالح',\n        'auth.passwordRequired': 'كلمة المرور مطلوبة',\n        'auth.passwordTooShort': 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',\n        'auth.firstNameRequired': 'الاسم الأول مطلوب',\n        'auth.lastNameRequired': 'الاسم الأخير مطلوب',\n        'auth.passwordRequirements': 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',\n        'auth.invalidCredentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',\n        'auth.emailAlreadyInUse': 'هذا البريد الإلكتروني مسجل بالفعل',\n        'auth.accountCreated': 'تم إنشاء الحساب بنجاح',\n        'auth.loginSuccess': 'تم تسجيل الدخول بنجاح',\n        // Cart\n        'cart.title': 'السلة',\n        'cart.empty': 'السلة فارغة',\n        'cart.emptyMessage': 'أضف بعض المنتجات إلى السلة للبدء',\n        'cart.continueShopping': 'مواصلة التسوق',\n        'cart.subtotal': 'المجموع الفرعي',\n        'cart.shipping': 'الشحن',\n        'cart.calculatedAtCheckout': 'يتم حسابها عند الدفع',\n        'cart.tax': 'الضريبة',\n        'cart.total': 'المجموع',\n        'cart.proceedToCheckout': 'المتابعة إلى الدفع',\n        'cart.remove': 'إزالة',\n        'cart.quantity': 'الكمية',\n        'cart.clearCart': 'تفريغ السلة',\n        'cart.orderSummary': 'ملخص الطلب',\n        // Wishlist\n        'wishlist.title': 'المفضلة',\n        'wishlist.empty': 'قائمة المفضلة فارغة',\n        'wishlist.emptyMessage': 'أضف عناصر إلى المفضلة لحفظها لوقت لاحق',\n        'wishlist.continueShopping': 'مواصلة التسوق',\n        'wishlist.clearAll': 'مسح الكل',\n        'wishlist.addAllToCart': 'إضافة الكل إلى السلة',\n        'wishlist.remove': 'إزالة',\n        'wishlist.addToCart': 'أضف إلى السلة',\n        'wishlist.alreadyInCart': 'موجود بالفعل في السلة',\n        // Account\n        'account.myAccount': 'حسابي',\n        'account.profile': 'الملف الشخصي',\n        'account.orders': 'الطلبات',\n        'account.addresses': 'العناوين',\n        'account.paymentMethods': 'طرق الدفع',\n        'account.wishlist': 'المفضلة',\n        'account.loyalty': 'برنامج الولاء',\n        'account.settings': 'الإعدادات',\n        'account.signOut': 'تسجيل الخروج',\n        'account.notLoggedIn': 'لم يتم تسجيل الدخول',\n        'account.loginRequired': 'تحتاج إلى تسجيل الدخول للوصول إلى حسابك',\n        'account.profileInformation': 'معلومات الملف الشخصي',\n        'account.firstName': 'الاسم الأول',\n        'account.lastName': 'الاسم الأخير',\n        'account.email': 'البريد الإلكتروني',\n        'account.phone': 'الهاتف',\n        'account.company': 'الشركة',\n        'account.emailCannotBeChanged': 'لا يمكن تغيير عنوان البريد الإلكتروني',\n        'account.saveChanges': 'حفظ التغييرات',\n        'account.profileUpdated': 'تم تحديث الملف الشخصي بنجاح',\n        'account.myOrders': 'طلباتي',\n        'account.shippingAddresses': 'عناوين الشحن',\n        // Orders\n        'orders.searchOrders': 'البحث في الطلبات',\n        'orders.allOrders': 'جميع الطلبات',\n        'orders.statusProcessing': 'قيد المعالجة',\n        'orders.statusShipped': 'تم الشحن',\n        'orders.statusDelivered': 'تم التسليم',\n        'orders.statusCancelled': 'ملغي',\n        'orders.noOrders': 'لم يتم العثور على طلبات',\n        'orders.noOrdersMatchingFilters': 'لا توجد طلبات تطابق المرشحات',\n        'orders.noOrdersYet': 'لم تقم بإجراء أي طلبات بعد',\n        'orders.items': 'عناصر',\n        'orders.orderItems': 'عناصر الطلب',\n        'orders.quantity': 'الكمية',\n        'orders.shippingAddress': 'عنوان الشحن',\n        'orders.tracking': 'معلومات التتبع',\n        'orders.trackPackage': 'تتبع الشحنة',\n        // Addresses\n        'addresses.addNew': 'إضافة عنوان جديد',\n        'addresses.editAddress': 'تعديل العنوان',\n        'addresses.addNewAddress': 'إضافة عنوان جديد',\n        'addresses.fullName': 'الاسم الكامل',\n        'addresses.streetAddress': 'عنوان الشارع',\n        'addresses.city': 'المدينة',\n        'addresses.stateProvince': 'الولاية/المنطقة',\n        'addresses.postalCode': 'الرمز البريدي',\n        'addresses.country': 'البلد',\n        'addresses.setAsDefault': 'تعيين كعنوان افتراضي',\n        'addresses.addAddress': 'إضافة عنوان',\n        'addresses.noAddresses': 'لم يتم العثور على عناوين',\n        'addresses.addAddressPrompt': 'أضف عنوان شحن لتسريع عملية الدفع',\n        'addresses.addFirst': 'أضف عنوانك الأول',\n        'addresses.default': 'افتراضي',\n        'addresses.setDefault': 'تعيين كافتراضي',\n        // Payment Methods\n        'payment.addNew': 'إضافة طريقة دفع جديدة',\n        'payment.editCard': 'تعديل البطاقة',\n        'payment.addNewCard': 'إضافة بطاقة جديدة',\n        'payment.cardNumber': 'رقم البطاقة',\n        'payment.cardholderName': 'اسم حامل البطاقة',\n        'payment.expiryDate': 'تاريخ الانتهاء',\n        'payment.cvv': 'رمز الأمان',\n        'payment.setAsDefault': 'تعيين كطريقة دفع افتراضية',\n        'payment.addCard': 'إضافة بطاقة',\n        'payment.noCards': 'لم يتم العثور على طرق دفع',\n        'payment.addCardPrompt': 'أضف طريقة دفع لتسريع عملية الدفع',\n        'payment.addFirst': 'أضف طريقة الدفع الأولى',\n        'payment.default': 'افتراضي',\n        'payment.setDefault': 'تعيين كافتراضي',\n        'payment.expires': 'تنتهي الصلاحية',\n        'payment.securityNote': 'لأغراض العرض فقط. لا تدخل معلومات بطاقة حقيقية.',\n        'payment.creditCard': 'بطاقة ائتمان',\n        'payment.paypal': 'باي بال',\n        'payment.bankTransfer': 'تحويل بنكي',\n        // Settings\n        'settings.password': 'كلمة المرور',\n        'settings.notifications': 'الإشعارات',\n        'settings.preferences': 'التفضيلات',\n        'settings.currentPassword': 'كلمة المرور الحالية',\n        'settings.newPassword': 'كلمة المرور الجديدة',\n        'settings.confirmPassword': 'تأكيد كلمة المرور',\n        'settings.changePassword': 'تغيير كلمة المرور',\n        'settings.passwordChanged': 'تم تغيير كلمة المرور بنجاح',\n        'settings.emailNotifications': 'تفعيل إشعارات البريد الإلكتروني',\n        'settings.orderUpdates': 'تحديثات حالة الطلب',\n        'settings.promotions': 'العروض والعروض الخاصة',\n        'settings.newsletter': 'الاشتراك في النشرة الإخبارية',\n        'settings.notificationsUpdated': 'تم تحديث تفضيلات الإشعارات',\n        'settings.savePreferences': 'حفظ التفضيلات',\n        'settings.language': 'اللغة',\n        'settings.currency': 'العملة',\n        'settings.theme': 'المظهر',\n        'settings.lightMode': 'الوضع الفاتح',\n        'settings.darkMode': 'الوضع الداكن',\n        // Common\n        'common.cancel': 'إلغاء',\n        'common.save': 'حفظ',\n        'common.edit': 'تعديل',\n        'common.delete': 'حذف',\n        // Wholesale Quote Form\n        'wholesale.wholesaleTitle': 'طلب عرض سعر بالجملة',\n        'wholesale.customProductTitle': 'طلب عرض سعر لمنتج مخصص',\n        'wholesale.companyName': 'اسم الشركة',\n        'wholesale.contactName': 'اسم جهة الاتصال',\n        'wholesale.email': 'البريد الإلكتروني',\n        'wholesale.phone': 'رقم الهاتف',\n        'wholesale.productType': 'نوع المنتج',\n        'wholesale.productTypePlaceholder': 'مثال: إلكترونيات، آلات، مواد خام',\n        'wholesale.specifications': 'مواصفات المنتج',\n        'wholesale.specificationsPlaceholder': 'يرجى تقديم مواصفات مفصلة لمتطلبات المنتج الخاص بك',\n        'wholesale.targetQuantity': 'الكمية المستهدفة',\n        'wholesale.targetQuantityPlaceholder': 'مثال: 1000 وحدة',\n        'wholesale.targetPrice': 'السعر المستهدف (اختياري)',\n        'wholesale.targetPricePlaceholder': 'نقطة السعر المرغوبة',\n        'wholesale.timeline': 'الجدول الزمني',\n        'wholesale.timelinePlaceholder': 'متى تحتاج المنتجات؟',\n        'wholesale.additionalNotes': 'ملاحظات إضافية',\n        'wholesale.additionalNotesPlaceholder': 'أي معلومات أخرى ترغب في مشاركتها',\n        'wholesale.uploadFiles': 'تحميل الملفات (اختياري)',\n        'wholesale.dropFilesHere': 'قم بإسقاط الملفات هنا أو انقر لتحميل رسومات المنتج أو المواصفات أو الصور المرجعية',\n        'wholesale.selectFiles': 'اختر الملفات',\n        'wholesale.cancel': 'إلغاء',\n        'wholesale.submitRequest': 'إرسال الطلب',\n        'wholesale.submitting': 'جاري الإرسال...',\n        'wholesale.requestSubmitted': 'تم إرسال الطلب',\n        'wholesale.thankYou': 'شكرًا لطلبك. سيتواصل فريقنا معك قريبًا.',\n        'wholesale.close': 'إغلاق',\n        'wholesale.authRequired': 'يجب تسجيل الدخول لتقديم طلب عرض سعر',\n        'wholesale.submitError': 'حدث خطأ أثناء إرسال طلبك. يرجى المحاولة مرة أخرى.',\n        // Common (Added placeholders)\n        'common.yes': '[ARABIC TEXT FOR KEY: common.yes]',\n        'common.no': '[ARABIC TEXT FOR KEY: common.no]',\n        'common.confirm': '[ARABIC TEXT FOR KEY: common.confirm]',\n        'common.close': '[ARABIC TEXT FOR KEY: common.close]',\n        'common.loading': '[ARABIC TEXT FOR KEY: common.loading]',\n        'common.error': '[ARABIC TEXT FOR KEY: common.error]',\n        'common.success': '[ARABIC TEXT FOR KEY: common.success]',\n        'common.previous': '[ARABIC TEXT FOR KEY: common.previous]',\n        'common.next': '[ARABIC TEXT FOR KEY: common.next]',\n        // Footer\n        'footer.quickLinks': 'روابط سريعة',\n        'footer.support': 'دعم العملاء',\n        'footer.contact': 'معلومات الاتصال',\n        'footer.rights': 'جميع الحقوق محفوظة'\n    }\n};\nfunction useLocale() {\n    // Extract language from the path\n    const pathname =  true ? window.location.pathname : 0;\n    const localeMatch = pathname.match(/^\\/(ar|en)/);\n    return localeMatch ? localeMatch[1] : 'en'; // English is the default language\n}\nfunction useTranslation() {\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_0__.useLanguageStore)();\n    const locale = useLocale();\n    // Use language from the path or from the store\n    const currentLanguage = locale || language;\n    const t = (key)=>{\n        return translations[currentLanguage][key] || key;\n    };\n    return {\n        t,\n        language: currentLanguage,\n        locale\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/translations/index.ts\n"));

/***/ })

});