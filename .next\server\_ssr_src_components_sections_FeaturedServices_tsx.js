"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_sections_FeaturedServices_tsx";
exports.ids = ["_ssr_src_components_sections_FeaturedServices_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/sections/FeaturedServices.tsx":
/*!******************************************************!*\
  !*** ./src/components/sections/FeaturedServices.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../stores/languageStore */ \"(ssr)/./src/stores/languageStore.ts\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CreditCardIcon,MessageCircleIcon,ShieldCheckIcon,TruckIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CreditCardIcon,MessageCircleIcon,ShieldCheckIcon,TruckIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CreditCardIcon,MessageCircleIcon,ShieldCheckIcon,TruckIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CreditCardIcon,MessageCircleIcon,ShieldCheckIcon,TruckIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CreditCardIcon,MessageCircleIcon,ShieldCheckIcon,TruckIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst services = [\n    {\n        id: 'shipping',\n        icon: _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: {\n            en: 'Free Shipping',\n            ar: 'شحن مجاني'\n        },\n        description: {\n            en: 'Free shipping on orders over $200',\n            ar: 'شحن مجاني للطلبات أكثر من 200 ريال'\n        },\n        color: 'text-blue-600',\n        bgColor: 'bg-blue-50'\n    },\n    {\n        id: 'security',\n        icon: _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: {\n            en: 'Secure Payment',\n            ar: 'دفع آمن'\n        },\n        description: {\n            en: '100% secure payment processing',\n            ar: 'معالجة دفع آمنة 100%'\n        },\n        color: 'text-green-600',\n        bgColor: 'bg-green-50'\n    },\n    {\n        id: 'support',\n        icon: _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: {\n            en: '24/7 Support',\n            ar: 'دعم 24/7'\n        },\n        description: {\n            en: 'Round-the-clock customer support',\n            ar: 'دعم العملاء على مدار الساعة'\n        },\n        color: 'text-purple-600',\n        bgColor: 'bg-purple-50'\n    },\n    {\n        id: 'returns',\n        icon: _barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: {\n            en: 'Easy Returns',\n            ar: 'إرجاع سهل'\n        },\n        description: {\n            en: '30-day hassle-free returns',\n            ar: 'إرجاع بدون متاعب لمدة 30 يوم'\n        },\n        color: 'text-orange-600',\n        bgColor: 'bg-orange-50'\n    }\n];\nconst FeaturedServices = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function FeaturedServices() {\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore)();\n    const isRTL = language === 'ar';\n    // Simple translation function\n    const t = (key, fallback)=>fallback;\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: 'easeOut'\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"text-center mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: t('services.title', 'Why Choose Us')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: t('services.description', 'We provide exceptional service and support to ensure your shopping experience is seamless and enjoyable.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: services.map((service)=>{\n                        const IconComponent = service.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"text-center group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `inline-flex items-center justify-center w-16 h-16 ${service.bgColor} rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: `w-8 h-8 ${service.color}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                    children: service.title[language]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: service.description[language]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, service.id, true, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"text-center mt-16 p-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: t('services.cta.title', 'Need Help?')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                            children: t('services.cta.description', 'Our customer service team is here to help you with any questions or concerns you may have.')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            size: \"lg\",\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    t('services.cta.button', 'Contact Support'),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CreditCardIcon_MessageCircleIcon_ShieldCheckIcon_TruckIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: `w-5 h-5 ${isRTL ? 'rotate-180' : ''}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ecommercepro\\\\src\\\\components\\\\sections\\\\FeaturedServices.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeaturedServices);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/FeaturedServices.tsx\n");

/***/ })

};
;