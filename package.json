{"name": "commercial-web-platform", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "db:init": "node scripts/init-db.js", "db:seed": "node scripts/seed-db.js", "db:reset": "npm run db:init && npm run db:seed", "setup": "npm install && npm run db:reset"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-slider": "^1.3.5", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.5", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "clsx": "^2.1.0", "framer-motion": "^12.10.4", "fuse.js": "^7.0.0", "lucide-react": "^0.344.0", "next": "^15.3.2", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-currency-input-field": "^3.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-live-chat-loader": "^2.8.0", "sqlite3": "^5.1.7", "tailwind-merge": "^2.2.1", "uuid": "^11.1.0", "zod": "^3.24.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@jest/globals": "^29.7.0", "@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^3.0.0", "@types/better-sqlite3": "^7.6.13", "@types/jest": "^29.5.6", "@types/node": "^20.10.5", "@types/react": "18.3.5", "@types/react-dom": "18.3.5", "autoprefixer": "^10.4.18", "critters": "^0.0.25", "eslint": "^8.56.0", "eslint-config-next": "15.3.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-loader": "^2.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "workbox-window": "^7.0.0"}, "overrides": {"@types/react": "18.3.5", "@types/react-dom": "18.3.5", "@testing-library/react": "^14.1.2"}}