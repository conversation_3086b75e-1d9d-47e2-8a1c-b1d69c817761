'use client';

import { memo, useState } from 'react';
import { motion } from 'framer-motion';
import { useLanguageStore } from '../../stores/languageStore';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { MailIcon, CheckCircleIcon } from 'lucide-react';
import toast from 'react-hot-toast';

const Newsletter = memo(function Newsletter() {
  const { language, t } = useLanguageStore();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      toast.error(t('newsletter.errors.emailRequired', 'Email is required'));
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast.error(t('newsletter.errors.emailInvalid', 'Please enter a valid email address'));
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsSubscribed(true);
      toast.success(t('newsletter.success', 'Successfully subscribed to newsletter!'));
      setEmail('');
    } catch (error) {
      toast.error(t('newsletter.errors.general', 'Something went wrong. Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubscribed) {
    return (
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center text-white"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <CheckCircleIcon className="w-16 h-16 mx-auto mb-4 text-green-300" />
            <h2 className="text-3xl font-bold mb-4">
              {t('newsletter.thankYou', 'Thank You!')}
            </h2>
            <p className="text-xl opacity-90">
              {t('newsletter.confirmationMessage', 'You\'ve been successfully subscribed to our newsletter.')}
            </p>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
      <div className="container mx-auto px-4">
        <motion.div
          className="max-w-4xl mx-auto text-center text-white"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          {/* Icon */}
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6"
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <MailIcon className="w-8 h-8" />
          </motion.div>

          {/* Content */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              {t('newsletter.title', 'Stay Updated')}
            </h2>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              {t('newsletter.description', 'Subscribe to our newsletter and be the first to know about new products, special offers, and exclusive deals.')}
            </p>
          </motion.div>

          {/* Newsletter Form */}
          <motion.form
            onSubmit={handleSubmit}
            className="max-w-md mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="email"
                  placeholder={t('newsletter.emailPlaceholder', 'Enter your email address')}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border-0 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50"
                  disabled={isLoading}
                />
              </div>
              <Button
                type="submit"
                disabled={isLoading}
                className="px-8 py-3 bg-white text-blue-600 hover:bg-gray-100 rounded-xl font-semibold transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    {t('newsletter.subscribing', 'Subscribing...')}
                  </div>
                ) : (
                  t('newsletter.subscribe', 'Subscribe')
                )}
              </Button>
            </div>
          </motion.form>

          {/* Privacy Notice */}
          <motion.p
            className="text-sm opacity-75 mt-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 0.75 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {t('newsletter.privacy', 'We respect your privacy. Unsubscribe at any time.')}
          </motion.p>

          {/* Benefits */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <div className="space-y-2">
              <div className="text-2xl font-bold">📧</div>
              <div className="font-semibold">{t('newsletter.benefits.updates', 'Latest Updates')}</div>
              <div className="text-sm opacity-75">{t('newsletter.benefits.updatesDesc', 'Get notified about new features')}</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold">🎁</div>
              <div className="font-semibold">{t('newsletter.benefits.offers', 'Exclusive Offers')}</div>
              <div className="text-sm opacity-75">{t('newsletter.benefits.offersDesc', 'Special discounts for subscribers')}</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold">⚡</div>
              <div className="font-semibold">{t('newsletter.benefits.early', 'Early Access')}</div>
              <div className="text-sm opacity-75">{t('newsletter.benefits.earlyDesc', 'Be first to try new products')}</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
});

export default Newsletter;
