import type { NextPage } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useTranslation } from '../translations';
import { useLanguage } from '../hooks/useLanguage';

// Components
import Layout from '../components/layout/Layout';
import { Button } from '../components/common/Button';
import { HomeIcon, SearchIcon, ChevronLeftIcon } from '../components/icons';

const NotFoundPage: NextPage = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { language, getLocalizedValue } = useLanguage();

  const pageTitle = getLocalizedValue(
    '404 - Page Not Found | Commerce Pro',
    '404 - الصفحة غير موجودة | كومرس برو'
  );

  const pageDescription = getLocalizedValue(
    'The page you are looking for could not be found.',
    'الصفحة التي تبحث عنها غير موجودة.'
  );

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <>
      <Head>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="robots" content="noindex, nofollow" />
        
        {/* Open Graph */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        
        {/* Twitter */}
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
      </Head>

      <Layout>
        <div className="min-h-screen flex items-center justify-center px-4 py-16">
          <div className="max-w-md w-full text-center">
            {/* 404 Illustration */}
            <div className="mb-8">
              <div className="text-9xl font-bold text-gray-200 mb-4">404</div>
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <SearchIcon size={32} className="text-gray-400" />
              </div>
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                {getLocalizedValue(
                  'Page Not Found',
                  'الصفحة غير موجودة'
                )}
              </h1>
              <p className="text-gray-600 mb-6">
                {getLocalizedValue(
                  'Sorry, we couldn\'t find the page you\'re looking for. It might have been moved, deleted, or you entered the wrong URL.',
                  'عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها. ربما تم نقلها أو حذفها أو أنك أدخلت رابطاً خاطئاً.'
                )}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={handleGoBack}
                  variant="outline"
                  leftIcon={<ChevronLeftIcon size={16} />}
                  className="flex-1 sm:flex-none"
                >
                  {getLocalizedValue('Go Back', 'العودة')}
                </Button>
                
                <Link href="/">
                  <Button
                    variant="primary"
                    leftIcon={<HomeIcon size={16} />}
                    className="flex-1 sm:flex-none"
                  >
                    {getLocalizedValue('Go Home', 'الصفحة الرئيسية')}
                  </Button>
                </Link>
              </div>

              <div className="pt-4">
                <Link href="/search">
                  <Button
                    variant="ghost"
                    leftIcon={<SearchIcon size={16} />}
                    className="w-full sm:w-auto"
                  >
                    {getLocalizedValue('Search Products', 'البحث في المنتجات')}
                  </Button>
                </Link>
              </div>
            </div>

            {/* Helpful Links */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                {getLocalizedValue('Popular Pages', 'الصفحات الشائعة')}
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <Link 
                  href="/shop" 
                  className="text-primary-600 hover:text-primary-700 transition-colors"
                >
                  {getLocalizedValue('Shop', 'المتجر')}
                </Link>
                <Link 
                  href="/services" 
                  className="text-primary-600 hover:text-primary-700 transition-colors"
                >
                  {getLocalizedValue('Services', 'الخدمات')}
                </Link>
                <Link 
                  href="/about" 
                  className="text-primary-600 hover:text-primary-700 transition-colors"
                >
                  {getLocalizedValue('About Us', 'من نحن')}
                </Link>
                <Link 
                  href="/contact" 
                  className="text-primary-600 hover:text-primary-700 transition-colors"
                >
                  {getLocalizedValue('Contact', 'اتصل بنا')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default NotFoundPage;
