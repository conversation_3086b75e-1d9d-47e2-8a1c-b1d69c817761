import type { NextPage } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useTranslation } from '../translations';
import { useLanguage } from '../hooks/useLanguage';

// Components
import Layout from '../components/layout/Layout';
import { Button } from '../components/common/Button';
import { HomeIcon, AlertCircleIcon, ChevronLeftIcon } from '../components/icons';

const ServerErrorPage: NextPage = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { language, getLocalizedValue } = useLanguage();

  const pageTitle = getLocalizedValue(
    '500 - Server Error | Commerce Pro',
    '500 - خطأ في الخادم | كومرس برو'
  );

  const pageDescription = getLocalizedValue(
    'An internal server error occurred. Please try again later.',
    'حدث خطأ داخلي في الخادم. يرجى المحاولة مرة أخرى لاحقاً.'
  );

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <>
      <Head>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="robots" content="noindex, nofollow" />
        
        {/* Open Graph */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        
        {/* Twitter */}
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
      </Head>

      <Layout>
        <div className="min-h-screen flex items-center justify-center px-4 py-16">
          <div className="max-w-md w-full text-center">
            {/* 500 Illustration */}
            <div className="mb-8">
              <div className="text-9xl font-bold text-gray-200 mb-4">500</div>
              <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircleIcon size={32} className="text-red-500" />
              </div>
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                {getLocalizedValue(
                  'Server Error',
                  'خطأ في الخادم'
                )}
              </h1>
              <p className="text-gray-600 mb-6">
                {getLocalizedValue(
                  'We\'re experiencing some technical difficulties. Our team has been notified and is working to fix the issue. Please try again in a few minutes.',
                  'نواجه بعض الصعوبات التقنية. تم إشعار فريقنا ويعمل على حل المشكلة. يرجى المحاولة مرة أخرى خلال بضع دقائق.'
                )}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={handleRefresh}
                  variant="primary"
                  className="flex-1 sm:flex-none"
                >
                  {getLocalizedValue('Try Again', 'حاول مرة أخرى')}
                </Button>
                
                <Button
                  onClick={handleGoBack}
                  variant="outline"
                  leftIcon={<ChevronLeftIcon size={16} />}
                  className="flex-1 sm:flex-none"
                >
                  {getLocalizedValue('Go Back', 'العودة')}
                </Button>
              </div>

              <div className="pt-4">
                <Link href="/">
                  <Button
                    variant="ghost"
                    leftIcon={<HomeIcon size={16} />}
                    className="w-full sm:w-auto"
                  >
                    {getLocalizedValue('Go Home', 'الصفحة الرئيسية')}
                  </Button>
                </Link>
              </div>
            </div>

            {/* Support Information */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                {getLocalizedValue('Need Help?', 'تحتاج مساعدة؟')}
              </h3>
              <div className="space-y-2 text-sm text-gray-600">
                <p>
                  {getLocalizedValue(
                    'If the problem persists, please contact our support team.',
                    'إذا استمرت المشكلة، يرجى الاتصال بفريق الدعم.'
                  )}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center mt-4">
                  <Link 
                    href="/contact" 
                    className="text-primary-600 hover:text-primary-700 transition-colors"
                  >
                    {getLocalizedValue('Contact Support', 'اتصل بالدعم')}
                  </Link>
                  <Link 
                    href="/help" 
                    className="text-primary-600 hover:text-primary-700 transition-colors"
                  >
                    {getLocalizedValue('Help Center', 'مركز المساعدة')}
                  </Link>
                </div>
              </div>
            </div>

            {/* Error ID for support */}
            <div className="mt-8 pt-4 border-t border-gray-100">
              <p className="text-xs text-gray-400">
                {getLocalizedValue('Error ID:', 'معرف الخطأ:')} {Date.now().toString(36)}
              </p>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ServerErrorPage;
